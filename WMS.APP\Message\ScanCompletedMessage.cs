﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace WMS.APP.Message
{
    /// <summary>
    /// 扫描完成消息
    /// </summary>
    public class ScanCompletedMessage
    {
        public string FromControlId { get; }
        public bool IsFinalField { get; }
        public string InputResult { get; }
        public bool IsManualInput { get; set; } = false;
        public DateTime ScanTime { get; } = DateTime.Now;

        public ScanCompletedMessage(string fromId, bool isFinal, string inputResult)
        {
            FromControlId = fromId;
            IsFinalField = isFinal;
            InputResult = inputResult;
        }
    }

    /// <summary>
    /// PDA广播消息
    /// </summary>
    public class PdaBroadcastMessage
    {
        public string ScanData { get; }
        public DateTime ScanTime { get; } = DateTime.Now;
        public string DeviceId { get; set; }

        public PdaBroadcastMessage(string scanData, string deviceId = null)
        {
            ScanData = scanData;
            DeviceId = deviceId;
        }
    }

    /// <summary>
    /// 扫描焦点变更消息
    /// </summary>
    public class ScanFocusChangedMessage
    {
        public string ControlId { get; }
        public DateTime ChangeTime { get; } = DateTime.Now;

        public ScanFocusChangedMessage(string controlId)
        {
            ControlId = controlId;
        }
    }

    /// <summary>
    /// 扫描流程完成消息
    /// </summary>
    public class ScanFlowCompletedMessage
    {
        public string FlowId { get; }
        public Dictionary<string, string> ScanResults { get; }
        public DateTime CompletedTime { get; } = DateTime.Now;

        public ScanFlowCompletedMessage(string flowId, Dictionary<string, string> results)
        {
            FlowId = flowId;
            ScanResults = results ?? new Dictionary<string, string>();
        }
    }
}
