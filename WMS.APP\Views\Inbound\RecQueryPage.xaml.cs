using WMS.APP.ViewModels.Inbound;

namespace WMS.APP.Views.Inbound;

public partial class RecQueryPage : ContentPage
{
    public RecQueryPage(RecQueryViewModel viewModel)
    {
        InitializeComponent();
        BindingContext = viewModel;
    }

    private async void OnSearchCompleted(object sender, EventArgs e)
    {
        if (BindingContext is RecQueryViewModel viewModel)
        {
            await viewModel.SearchCommand.ExecuteAsync(null);
        }
    }
}
