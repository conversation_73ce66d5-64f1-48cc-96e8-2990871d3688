﻿using System.ComponentModel;
using System.Runtime.CompilerServices;
using WMS.APP.Common;
using WMS.SERVICE;

namespace WMS.APP.ViewModel
{
    // LoginViewModel.cs
    public class LoginViewModel : INotifyPropertyChanged
    {
        private readonly INavigation _navigation;

        //private readonly IServiceProvider _serviceProvider;

        private readonly INavigationService _navigationService;

        private readonly IUserService _userService;

        public LoginViewModel(INavigationService navigationService, IUserService userService)
        {
            //_navigation = navigation;
            _userService = userService;
            //_serviceProvider = serviceProvider;
            _navigationService = navigationService;
            LoginCommand = new Command(ExecuteLogin, CanExecuteLogin);
            CloseCommand = new Command(ExecuteClose);
        }

        // 属性
        private string _userName;
        public string UserName
        {
            get => _userName;
            set
            {
                _userName = value;
                OnPropertyChanged();
                ((Command)LoginCommand).ChangeCanExecute();
            }
        }

        private string _password;
        public string Password
        {
            get => _password;
            set
            {
                _password = value;
                OnPropertyChanged();
                ((Command)LoginCommand).ChangeCanExecute();
            }
        }

        private bool _rememberPassword;
        public bool RememberPassword
        {
            get => _rememberPassword;
            set
            {
                _rememberPassword = value;
                OnPropertyChanged();
                SaveCredentials();
            }
        }

        private string _statusMessage;
        public string StatusMessage
        {
            get => _statusMessage;
            set
            {
                _statusMessage = value;
                OnPropertyChanged();
                OnPropertyChanged(nameof(HasErrorMessage));
            }
        }

        public bool HasErrorMessage => !string.IsNullOrEmpty(StatusMessage);

        // 命令
        public Command LoginCommand { get; }
        public Command CloseCommand { get; }



        // 方法
        private bool CanExecuteLogin() =>
            !string.IsNullOrWhiteSpace(UserName) &&
            !string.IsNullOrWhiteSpace(Password);


        /// <summary>
        /// 登录PDA程序
        /// </summary>
        private async void ExecuteLogin()
        {
            try
            {
                StatusMessage = "";
                string loginresult = await _userService.GetUserInfoAsync(UserName, Password);
                if (!string.IsNullOrWhiteSpace(loginresult))
                {
                    StatusMessage = "登录失败，请查看登录信息！";
                }
                // 这里可以根据实际情况进行登录验证
                if (loginresult == "SUCCESS")
                {
                    StatusMessage = "登录成功！";
                    SaveCredentials();
                    _navigationService.CloseCurrentAsync();
                    await Shell.Current.GoToAsync($"//MainShell", animate: true);
                }
                else
                {
                    StatusMessage = "登录失败，请检查用户名和密码！";
                    return;
                }
            }
            catch (Exception ex)
            {
                StatusMessage = $"登录失败: {ex.Message}";
            }
        }

        private void ExecuteClose() =>
            Application.Current?.Quit();

        // 保存凭据
        private void SaveCredentials()
        {
            if (RememberPassword)
            {
                Preferences.Set("UserName", UserName);
                Preferences.Set("Password", Password);
            }
            else
            {
                Preferences.Remove("UserName");
                Preferences.Remove("Password");
            }
        }

        // 加载保存的凭据
        public void LoadSavedCredentials()
        {
            UserName = Preferences.Get("UserName", "");
            Password = Preferences.Get("Password", "");
            RememberPassword = !string.IsNullOrEmpty(UserName) && !string.IsNullOrEmpty(Password);
        }
        // INotifyPropertyChanged
        public event PropertyChangedEventHandler? PropertyChanged;
        protected virtual void OnPropertyChanged([CallerMemberName] string propertyName = null)
        {
            PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
        }
    }
}
