<?xml version="1.0" encoding="utf-8" ?>
<ContentPage xmlns="http://schemas.microsoft.com/dotnet/2021/maui"
             xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
             xmlns:vm="clr-namespace:WMS.APP.ViewModels"
             x:Class="WMS.APP.Views.inbound.ReceiveOrderEditPage"
             Title="收货单编辑">

    <ContentPage.ToolbarItems>
        <ToolbarItem Text="保存" Command="{Binding SaveOrderCommand}" IconImageSource="save.png" />
        <ToolbarItem Text="明细" Command="{Binding EditDetailsCommand}" IconImageSource="list.png" />
    </ContentPage.ToolbarItems>

    <ScrollView>
        <VerticalStackLayout Padding="20" Spacing="15">
            <!-- 订单基本信息 -->
            <Grid ColumnDefinitions="120,*" RowDefinitions="Auto,Auto,Auto,Auto,Auto" RowSpacing="15" ColumnSpacing="10">
                <Label Text="订单号:" Grid.Row="0" Grid.Column="0" VerticalOptions="Center" />
                <Entry Text="{Binding CurrentOrder.OrderNumber}" Grid.Row="0" Grid.Column="1" />

                <Label Text="供应商:" Grid.Row="1" Grid.Column="0" VerticalOptions="Center" />
                <Entry Text="{Binding CurrentOrder.Supplier}" Grid.Row="1" Grid.Column="1" />

                <Label Text="预计到货日期:" Grid.Row="2" Grid.Column="0" VerticalOptions="Center" />
                <DatePicker Date="{Binding CurrentOrder.ExpectedDate}" Grid.Row="2" Grid.Column="1" />

                <Label Text="备注:" Grid.Row="3" Grid.Column="0" VerticalOptions="Center" />
                <Editor Text="{Binding CurrentOrder.Remarks}" Grid.Row="3" Grid.Column="1" HeightRequest="100" />

                <Label Text="状态:" Grid.Row="4" Grid.Column="0" VerticalOptions="Center" />
                <Picker Grid.Row="4" Grid.Column="1" ItemsSource="{Binding StatusOptions}" SelectedItem="{Binding CurrentOrder.Status}" />
            </Grid>

            <!-- 订单汇总信息 -->
            <Frame BorderColor="LightGray" Padding="10" Margin="0,20,0,0">
                <VerticalStackLayout Spacing="10">
                    <Label Text="订单汇总信息" FontAttributes="Bold" />
                    <Grid ColumnDefinitions="*,*" RowDefinitions="Auto,Auto" RowSpacing="10" ColumnSpacing="10">
                        <Label Text="{Binding CurrentOrder.TotalItems, StringFormat='总物料数: {0}'}" Grid.Row="0" Grid.Column="0" />
                        <Label Text="{Binding CurrentOrder.TotalQuantity, StringFormat='总数量: {0}'}" Grid.Row="0" Grid.Column="1" />
                        <Label Text="{Binding CurrentOrder.CompletedItems, StringFormat='已收货: {0}'}" Grid.Row="1" Grid.Column="0" />
                        <Label Text="{Binding CurrentOrder.Status, StringFormat='状态: {0}'}" Grid.Row="1" Grid.Column="1" />
                    </Grid>
                </VerticalStackLayout>
            </Frame>
        </VerticalStackLayout>
    </ScrollView>
</ContentPage>