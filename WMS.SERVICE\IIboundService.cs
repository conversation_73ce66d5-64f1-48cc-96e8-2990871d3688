﻿using WMS.MODEL;

namespace WMS.SERVICE
{
    public interface IIboundService
    {

        // 收货单相关方法
        Task<List<ReceiveOrderModel>> GetReceiveOrdersAsync(string keyword = null);
        Task<ReceiveOrderModel> GetReceiveOrderByIdAsync(string id);
        Task SaveReceiveOrderAsync(ReceiveOrderModel order);
        Task DeleteReceiveOrderAsync(string id);

        // 清理临时存储
        Task ClearReceiveOrdersTempStorageAsync();

        // 添加分页获取订单的方法
        Task<List<ReceiveOrderModel>> GetReceiveOrdersPagedAsync(string keyword, int page, int pageSize);
    }
}
