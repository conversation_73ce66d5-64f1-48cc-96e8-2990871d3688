﻿using System.Security.Cryptography;
using System.Text;
using System.Text.Json;

namespace WMS.SERVICE.Utils
{
    /**
     * 请求API的帮助类
     */
    public class ApiHelperr
    {
        private readonly HttpClient _httpClient;

        public ApiHelperr(TimeSpan? timeout = null)
        {
            _httpClient = new HttpClient();

            if (timeout.HasValue)
            {
                _httpClient.Timeout = timeout.Value;
            }
        }

        // MD5 加密
        public static string ToMd5(string input)
        {
            using var md5 = MD5.Create();
            var inputBytes = Encoding.UTF8.GetBytes(input);
            var hashBytes = md5.ComputeHash(inputBytes);
            var sb = new StringBuilder();

            foreach (var b in hashBytes)
                sb.Append(b.ToString("x2"));

            return sb.ToString();
        }

        // GET 请求
        public async Task<string> GetAsync(string url, Dictionary<string, string>? headers = null)
        {
            try
            {
                var request = new HttpRequestMessage(HttpMethod.Get, url);

                if (headers != null)
                {
                    foreach (var kv in headers)
                        request.Headers.Add(kv.Key, kv.Value);
                }
                using var response = await _httpClient.SendAsync(request);
                response.EnsureSuccessStatusCode();
                return await response.Content.ReadAsStringAsync();
            }
            catch (Exception ex)
            {
                return $"Error: {ex.Message}"; // 返回错误信息
            }

        }

        // POST 请求（发送 JSON）
        public async Task<string> PostAsync<T>(string url, T body, Dictionary<string, string>? headers = null)
        {
            try
            {
                var json = JsonSerializer.Serialize(body);
                var content = new StringContent(json, Encoding.UTF8, "application/json");
                var request = new HttpRequestMessage(HttpMethod.Post, url)
                {
                    Content = content
                };

                if (headers != null)
                {
                    foreach (var kv in headers)
                        request.Headers.Add(kv.Key, kv.Value);
                }

                using var response = await _httpClient.SendAsync(request);
                response.EnsureSuccessStatusCode();
                return await response.Content.ReadAsStringAsync();
            }
            catch (Exception ex)
            {
                return $"Error: {ex.Message}"; // 返回错误信息
            }

        }
    }
}