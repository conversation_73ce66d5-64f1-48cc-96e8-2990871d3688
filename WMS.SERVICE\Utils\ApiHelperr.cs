﻿using System.Security.Cryptography;
using System.Text;
using System.Text.Json;

namespace WMS.SERVICE.Utils
{
    /**
     * 请求API的帮助类
     */
    public class ApiHelperr
    {
        private readonly HttpClient _httpClient;

        public ApiHelperr(TimeSpan? timeout = null)
        {
            _httpClient = new HttpClient();

            if (timeout.HasValue)
            {
                _httpClient.Timeout = timeout.Value;
            }
        }

        // MD5 加密
        public static string ToMd5(string input)
        {
            using var md5 = MD5.Create();
            var inputBytes = Encoding.UTF8.GetBytes(input);
            var hashBytes = md5.ComputeHash(inputBytes);
            var sb = new StringBuilder();

            foreach (var b in hashBytes)
                sb.Append(b.ToString("x2"));

            return sb.ToString();
        }

        // GET 请求 - 泛型版本
        public async Task<T?> GetAsync<T>(string url, Dictionary<string, string>? headers = null)
        {
            try
            {
                var request = new HttpRequestMessage(HttpMethod.Get, url);

                if (headers != null)
                {
                    foreach (var kv in headers)
                        request.Headers.Add(kv.Key, kv.Value);
                }
                using var response = await _httpClient.SendAsync(request);
                response.EnsureSuccessStatusCode();

                var content = await response.Content.ReadAsStringAsync();
                if (string.IsNullOrEmpty(content))
                    return default(T);

                return JsonSerializer.Deserialize<T>(content, new JsonSerializerOptions
                {
                    PropertyNameCaseInsensitive = true
                });
            }
            catch (Exception ex)
            {
                Console.WriteLine($"GET请求失败: {ex.Message}");
                return default(T);
            }
        }

        // GET 请求 - 字符串版本
        public async Task<string> GetAsync(string url, Dictionary<string, string>? headers = null)
        {
            try
            {
                var request = new HttpRequestMessage(HttpMethod.Get, url);

                if (headers != null)
                {
                    foreach (var kv in headers)
                        request.Headers.Add(kv.Key, kv.Value);
                }
                using var response = await _httpClient.SendAsync(request);
                response.EnsureSuccessStatusCode();
                return await response.Content.ReadAsStringAsync();
            }
            catch (Exception ex)
            {
                return $"Error: {ex.Message}"; // 返回错误信息
            }

        }

        // POST 请求 - 泛型版本
        public async Task<TResult?> PostAsync<TBody, TResult>(string url, TBody body, Dictionary<string, string>? headers = null)
        {
            try
            {
                var json = JsonSerializer.Serialize(body);
                var content = new StringContent(json, Encoding.UTF8, "application/json");
                var request = new HttpRequestMessage(HttpMethod.Post, url)
                {
                    Content = content
                };

                if (headers != null)
                {
                    foreach (var kv in headers)
                        request.Headers.Add(kv.Key, kv.Value);
                }

                using var response = await _httpClient.SendAsync(request);
                response.EnsureSuccessStatusCode();

                var responseContent = await response.Content.ReadAsStringAsync();
                if (string.IsNullOrEmpty(responseContent))
                    return default(TResult);

                return JsonSerializer.Deserialize<TResult>(responseContent, new JsonSerializerOptions
                {
                    PropertyNameCaseInsensitive = true
                });
            }
            catch (Exception ex)
            {
                Console.WriteLine($"POST请求失败: {ex.Message}");
                return default(TResult);
            }
        }

        // POST 请求（发送 JSON）- 字符串版本
        public async Task<string> PostAsync<T>(string url, T body, Dictionary<string, string>? headers = null)
        {
            try
            {
                var json = JsonSerializer.Serialize(body);
                var content = new StringContent(json, Encoding.UTF8, "application/json");
                var request = new HttpRequestMessage(HttpMethod.Post, url)
                {
                    Content = content
                };

                if (headers != null)
                {
                    foreach (var kv in headers)
                        request.Headers.Add(kv.Key, kv.Value);
                }

                using var response = await _httpClient.SendAsync(request);
                response.EnsureSuccessStatusCode();
                return await response.Content.ReadAsStringAsync();
            }
            catch (Exception ex)
            {
                return $"Error: {ex.Message}"; // 返回错误信息
            }
        }

        // PUT 请求 - 泛型版本
        public async Task<TResult?> PutAsync<TBody, TResult>(string url, TBody body, Dictionary<string, string>? headers = null)
        {
            try
            {
                var json = JsonSerializer.Serialize(body);
                var content = new StringContent(json, Encoding.UTF8, "application/json");
                var request = new HttpRequestMessage(HttpMethod.Put, url)
                {
                    Content = content
                };

                if (headers != null)
                {
                    foreach (var kv in headers)
                        request.Headers.Add(kv.Key, kv.Value);
                }

                using var response = await _httpClient.SendAsync(request);
                response.EnsureSuccessStatusCode();

                var responseContent = await response.Content.ReadAsStringAsync();
                if (string.IsNullOrEmpty(responseContent))
                    return default(TResult);

                return JsonSerializer.Deserialize<TResult>(responseContent, new JsonSerializerOptions
                {
                    PropertyNameCaseInsensitive = true
                });
            }
            catch (Exception ex)
            {
                Console.WriteLine($"PUT请求失败: {ex.Message}");
                return default(TResult);
            }
        }

        // PUT 请求 - 字符串版本
        public async Task<string> PutAsync<T>(string url, T body, Dictionary<string, string>? headers = null)
        {
            try
            {
                var json = JsonSerializer.Serialize(body);
                var content = new StringContent(json, Encoding.UTF8, "application/json");
                var request = new HttpRequestMessage(HttpMethod.Put, url)
                {
                    Content = content
                };

                if (headers != null)
                {
                    foreach (var kv in headers)
                        request.Headers.Add(kv.Key, kv.Value);
                }

                using var response = await _httpClient.SendAsync(request);
                response.EnsureSuccessStatusCode();
                return await response.Content.ReadAsStringAsync();
            }
            catch (Exception ex)
            {
                return $"Error: {ex.Message}";
            }
        }

        // DELETE 请求 - 返回bool
        public async Task<bool> DeleteAsync(string url, Dictionary<string, string>? headers = null)
        {
            try
            {
                var request = new HttpRequestMessage(HttpMethod.Delete, url);

                if (headers != null)
                {
                    foreach (var kv in headers)
                        request.Headers.Add(kv.Key, kv.Value);
                }

                using var response = await _httpClient.SendAsync(request);
                response.EnsureSuccessStatusCode();
                return true;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"DELETE请求失败: {ex.Message}");
                return false;
            }
        }

        // DELETE 请求 - 返回字符串
        public async Task<string> DeleteStringAsync(string url, Dictionary<string, string>? headers = null)
        {
            try
            {
                var request = new HttpRequestMessage(HttpMethod.Delete, url);

                if (headers != null)
                {
                    foreach (var kv in headers)
                        request.Headers.Add(kv.Key, kv.Value);
                }

                using var response = await _httpClient.SendAsync(request);
                response.EnsureSuccessStatusCode();
                return await response.Content.ReadAsStringAsync();
            }
            catch (Exception ex)
            {
                return $"Error: {ex.Message}";
            }
        }

        public void Dispose()
        {
            _httpClient?.Dispose();
        }
    }
}