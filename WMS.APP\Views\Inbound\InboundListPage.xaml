<?xml version="1.0" encoding="utf-8" ?>
<ContentPage
    x:Class="WMS.APP.Views.inbound.InboundListPage"
    xmlns="http://schemas.microsoft.com/dotnet/2021/maui"
    xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
    Title="收货订单列表">

	<ContentPage.Content>
		<VerticalStackLayout Padding="5" Spacing="1">
			<!-- 搜索区域：上下结构，左侧标签，右侧输入 -->
			<Frame Padding="10" CornerRadius="8" BackgroundColor="#f5f5f5">
				<Grid ColumnDefinitions="Auto,*,Auto" RowDefinitions="Auto,Auto" ColumnSpacing="5">
					<Label Grid.Row="0" Grid.Column="0" Text="系统订单号" VerticalOptions="Center" HorizontalOptions="End" />
					<Entry Grid.Row="0" Grid.Column="1" Placeholder="请输入订单号" Text="{Binding SearchOrderNumber}" HorizontalOptions="FillAndExpand"/>
					<Label Grid.Row="1" Grid.Column="0" Text="客户订单号" VerticalOptions="Center" HorizontalOptions="End" />
					<Entry Grid.Row="1" Grid.Column="1" Placeholder="请输入客户订单号" Text="{Binding SearchCustomerOrderNumber}" HorizontalOptions="FillAndExpand"/>
					<Button Grid.Row="1" Grid.Column="2" Text="查询" Command="{Binding SearchCommand}" Margin="10,0,0,0" HorizontalOptions="Start"/>
				</Grid>
			</Frame>

			<!-- 列表区域：左右结构，左侧标签，右侧内容 -->
			<CollectionView ItemsSource="{Binding Orders}" SelectionMode="None">
				<CollectionView.ItemTemplate>
					<DataTemplate>
						<Frame Margin="0,5" Padding="5" CornerRadius="10" BorderColor="#e0e0e0" HasShadow="True">
							<Grid ColumnDefinitions="Auto,*,Auto" RowDefinitions="Auto,Auto,Auto,Auto,Auto,Auto" ColumnSpacing="10" RowSpacing="6">
								<Label Grid.Row="0" Grid.Column="0" Text="订单号：" FontAttributes="Bold" FontSize="14" VerticalOptions="Center"/>
								<Label Grid.Row="0" Grid.Column="1" Text="{Binding OrderNumber}" FontSize="14" VerticalOptions="Center"/>

								<Label Grid.Row="1" Grid.Column="0" Text="客户订单号："  VerticalOptions="Center"/>
								<Label Grid.Row="1" Grid.Column="1" Text="{Binding Supplier}"  VerticalOptions="Center"/>

								<Label Grid.Row="2" Grid.Column="0" Text="订单状态："  VerticalOptions="Center"/>
								<Label Grid.Row="2" Grid.Column="1" Text="{Binding Status}"  VerticalOptions="Center"/>

								<Label Grid.Row="3" Grid.Column="0" Text="货主："  VerticalOptions="Center"/>
								<Label Grid.Row="3" Grid.Column="1" Text="{Binding Owner}"  VerticalOptions="Center"/>

								<Label Grid.Row="4" Grid.Column="0" Text="订单类型："  VerticalOptions="Center"/>
								<Label Grid.Row="4" Grid.Column="1" Text="{Binding OrderType}"  VerticalOptions="Center"/>

								<Button Grid.Row="0" Grid.Column="2" Grid.RowSpan="5"
                                        Text="去收货"
                                        Command="{Binding Path=BindingContext.GoReceiveCommand}"
                                        CommandParameter="{Binding .}"
                                        VerticalOptions="Center"
                                        Margin="10,0,0,0"
                                        />
							</Grid>
						</Frame>
					</DataTemplate>
				</CollectionView.ItemTemplate>
			</CollectionView>
		</VerticalStackLayout>
	</ContentPage.Content>
</ContentPage>