<?xml version="1.0" encoding="utf-8" ?>
<ContentView xmlns="http://schemas.microsoft.com/dotnet/2021/maui"
             xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
             x:Class="WMS.APP.UserControl.ScanEntryView"
             x:Name="root">
    <Frame x:Name="containerFrame"
           Padding="10"
           CornerRadius="8"
           BorderColor="{Binding BorderColor, Source={x:Reference root}}"
           BackgroundColor="{Binding BackgroundColor, Source={x:Reference root}}"
           HasShadow="False">
        <VerticalStackLayout Spacing="8">
            <Label Text="{Binding Title, Source={x:Reference root}}"
                   FontSize="16"
                   FontAttributes="Bold"
                   TextColor="{Binding TitleColor, Source={x:Reference root}}"/>
            <Entry x:Name="entry"
                   Text="{Binding ScanText, Source={x:Reference root}, Mode=TwoWay}"
                   Completed="Entry_Completed"
                   Focused="OnEntryFocused"
                   Unfocused="OnEntryUnfocused"
                   TextChanged="OnTextChanged"
                   ReturnType="Next"
                   FontSize="18"
                   HeightRequest="45"
                   Placeholder="{Binding Placeholder, Source={x:Reference root}}"
                   ClearButtonVisibility="WhileEditing"
                   IsReadOnly="{Binding IsReadOnly, Source={x:Reference root}}"
                   BackgroundColor="Transparent"/>
            <!-- 状态指示器 -->
            <Label x:Name="statusLabel"
                   Text="{Binding StatusText, Source={x:Reference root}}"
                   FontSize="12"
                   TextColor="{Binding StatusColor, Source={x:Reference root}}"
                   IsVisible="{Binding ShowStatus, Source={x:Reference root}}"/>
        </VerticalStackLayout>
    </Frame>
</ContentView>
