<?xml version="1.0" encoding="utf-8" ?>
<ContentPage xmlns="http://schemas.microsoft.com/dotnet/2021/maui"
             xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
             x:Class="WMS.APP.Views.Outbound.OutboundMenuPage"
             Title="出库管理">
    
    <ScrollView>
        <VerticalStackLayout Padding="20" Spacing="15">
            <!-- 页面标题 -->
            <Label Text="出库管理" 
                   FontSize="24" 
                   FontAttributes="Bold" 
                   HorizontalOptions="Center" 
                   Margin="0,0,0,20"/>
            
            <!-- 分拣模块 -->
            <Frame Padding="15" CornerRadius="10" BackgroundColor="#FCE4EC">
                <VerticalStackLayout Spacing="10">
                    <Label Text="分拣管理" FontSize="18" FontAttributes="Bold" TextColor="#C2185B"/>
                    <Grid ColumnDefinitions="*,*" RowDefinitions="Auto" ColumnSpacing="10" RowSpacing="10">
                        <!-- 分拣查询 -->
                        <Button Grid.Row="0" Grid.Column="0" 
                                Text="分拣查询" 
                                BackgroundColor="#E91E63" 
                                TextColor="White"
                                Clicked="OnPkmQueryClicked"/>
                        <!-- 分拣扫描 -->
                        <Button Grid.Row="0" Grid.Column="1" 
                                Text="分拣扫描" 
                                BackgroundColor="#E91E63" 
                                TextColor="White"
                                Clicked="OnPkmScanClicked"/>
                    </Grid>
                </VerticalStackLayout>
            </Frame>
            
            <!-- 统计信息 -->
            <Frame Padding="15" CornerRadius="10" BackgroundColor="#F3E5F5">
                <VerticalStackLayout Spacing="10">
                    <Label Text="今日统计" FontSize="16" FontAttributes="Bold" TextColor="#7B1FA2"/>
                    <Grid ColumnDefinitions="*,*,*" RowDefinitions="Auto,Auto" ColumnSpacing="10" RowSpacing="5">
                        <Label Grid.Row="0" Grid.Column="0" Text="待分拣" HorizontalOptions="Center" FontSize="12"/>
                        <Label Grid.Row="0" Grid.Column="1" Text="进行中" HorizontalOptions="Center" FontSize="12"/>
                        <Label Grid.Row="0" Grid.Column="2" Text="已完成" HorizontalOptions="Center" FontSize="12"/>
                        
                        <Label Grid.Row="1" Grid.Column="0" Text="0" HorizontalOptions="Center" FontSize="18" FontAttributes="Bold" TextColor="#E91E63"/>
                        <Label Grid.Row="1" Grid.Column="1" Text="0" HorizontalOptions="Center" FontSize="18" FontAttributes="Bold" TextColor="#FF9800"/>
                        <Label Grid.Row="1" Grid.Column="2" Text="0" HorizontalOptions="Center" FontSize="18" FontAttributes="Bold" TextColor="#4CAF50"/>
                    </Grid>
                </VerticalStackLayout>
            </Frame>
        </VerticalStackLayout>
    </ScrollView>
</ContentPage>
