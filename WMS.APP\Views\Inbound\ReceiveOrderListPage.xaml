<?xml version="1.0" encoding="utf-8" ?>
<ContentPage xmlns="http://schemas.microsoft.com/dotnet/2021/maui"
             xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
             xmlns:vm="clr-namespace:WMS.APP.ViewModels"
             xmlns:toolkit="http://schemas.microsoft.com/dotnet/2022/maui/toolkit"
             xmlns:model="clr-namespace:WMS.MODEL;assembly=WMS.MODEL"
             x:Class="WMS.APP.Views.inbound.ReceiveOrderListPage"
             x:Name="receiveOrderListPage"
             Title="收货单管理">

    <ContentPage.ToolbarItems>
        <ToolbarItem Text="新建" Command="{Binding CreateOrderCommand}" IconImageSource="add.png" />
    </ContentPage.ToolbarItems>

    <Grid RowDefinitions="Auto,*">
        <!-- 搜索栏 -->
        <Grid ColumnDefinitions="*,Auto" Padding="10" HeightRequest="65">
            <Entry Placeholder="输入订单号或供应商搜索" Text="{Binding SearchKeyword}" />
            <Button Grid.Column="1" Text="查询"  BackgroundColor="DarkOrange" Command="{Binding SearchOrderCommand}" />
        </Grid>


        <!-- 订单列表 - 添加RefreshView支持下拉刷新 -->
        <!--<RefreshView Grid.Row="1" 
                    IsRefreshing="{Binding IsRefreshing}" 
                    Command="{Binding RefreshCommand}">-->
        <CollectionView x:Name="ordersCollection"
                            Grid.Row="1" 
                           ItemsSource="{Binding Orders}"
                           RemainingItemsThreshold="1"
                           RemainingItemsThresholdReached="CollectionView_RemainingItemsThresholdReached">
            <CollectionView.ItemsLayout>
                <LinearItemsLayout Orientation="Vertical" ItemSpacing="10" />
            </CollectionView.ItemsLayout>

            <CollectionView.EmptyView>
                <VerticalStackLayout HorizontalOptions="Center" VerticalOptions="Center">
                    <Image Source="empty_list.png" HeightRequest="100" />
                    <Label Text="暂无订单数据" HorizontalOptions="Center" Margin="0,10,0,0" />
                </VerticalStackLayout>
            </CollectionView.EmptyView>

            <CollectionView.Footer>
                <Grid Padding="20" HeightRequest="60">
                    <!-- 加载中指示器 -->
                    <StackLayout x:Name="loadingIndicator" IsVisible="{Binding IsLoadingMore}">
                        <ActivityIndicator IsRunning="{Binding IsLoadingMore}" 
                                              HorizontalOptions="Center" 
                                              VerticalOptions="Center" 
                                              Color="#2196F3" />
                        <Label Text="正在加载更多..." 
                                  HorizontalOptions="Center" 
                                  Margin="0,5,0,0" />
                    </StackLayout>

                    <!-- 没有更多数据提示 -->
                    <Label Text="没有更多数据了" 
                               IsVisible="{Binding IsNoMoreData}"
                               HorizontalOptions="Center"
                               VerticalOptions="Center"
                               TextColor="Gray" />
                </Grid>
            </CollectionView.Footer>

            <CollectionView.ItemTemplate>
                <DataTemplate x:DataType="model:ReceiveOrderModel">
                    <Frame Margin="10,5" Padding="15" BorderColor="LightGray" CornerRadius="10" HasShadow="True">
                        <Grid RowDefinitions="Auto,Auto,Auto" RowSpacing="10">
                            <!-- 订单头部信息 -->
                            <Grid Grid.Row="0" ColumnDefinitions="*,Auto">
                                <Label Text="{Binding OrderNumber}" FontAttributes="Bold" FontSize="16" VerticalOptions="Center" />
                                <Label Grid.Column="1" Text="{Binding Status}" 
                                           BackgroundColor="#E0F7FA" Padding="8,3"
                                           FontSize="14" TextColor="#006064" />
                            </Grid>

                            <!-- 订单详细信息 -->
                            <Grid Grid.Row="1" ColumnDefinitions="*,*" RowDefinitions="Auto,Auto" Margin="0,5">
                                <Label Grid.Row="0" Grid.Column="0" Text="{Binding Supplier, StringFormat='供应商: {0}'}" />
                                <Label Grid.Row="0" Grid.Column="1" Text="{Binding CreateDate, StringFormat='创建日期: {0:yyyy-MM-dd}'}" />
                                <Label Grid.Row="1" Grid.Column="0" Text="{Binding TotalItems, StringFormat='物料数: {0}'}" />
                                <Label Grid.Row="1" Grid.Column="1" Text="{Binding TotalQuantity, StringFormat='总数量: {0}'}" />
                            </Grid>

                            <!-- 操作按钮 -->
                            <HorizontalStackLayout Grid.Row="2" HorizontalOptions="End" Spacing="10">
                                <Button Text="编辑" HeightRequest="35" WidthRequest="70" BackgroundColor="#2196F3">
                                    <Button.Behaviors>
                                        <toolkit:EventToCommandBehavior
                                                EventName="Clicked"
                                                Command="{Binding Source={x:Reference receiveOrderListPage}, Path=BindingContext.EditOrderCommand}"
                                                CommandParameter="{Binding .}" />
                                    </Button.Behaviors>
                                </Button>
                                <Button Text="删除" HeightRequest="35" WidthRequest="70" BackgroundColor="#F44336">
                                    <Button.Behaviors>
                                        <toolkit:EventToCommandBehavior
                                                EventName="Clicked"
                                                Command="{Binding Source={x:Reference receiveOrderListPage}, Path=BindingContext.DeleteOrderCommand}"
                                                CommandParameter="{Binding .}" />
                                    </Button.Behaviors>
                                </Button>
                            </HorizontalStackLayout>
                        </Grid>
                    </Frame>
                </DataTemplate>
            </CollectionView.ItemTemplate>
        </CollectionView>
        <!--</RefreshView>-->
    </Grid>
</ContentPage>







