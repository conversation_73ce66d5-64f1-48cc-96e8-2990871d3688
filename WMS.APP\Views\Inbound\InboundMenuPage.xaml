<?xml version="1.0" encoding="utf-8" ?>
<ContentPage xmlns="http://schemas.microsoft.com/dotnet/2021/maui"
             xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
             x:Class="WMS.APP.Views.Inbound.InboundMenuPage"
             Title="入库管理">
    
    <ScrollView>
        <VerticalStackLayout Padding="20" Spacing="15">
            <!-- 页面标题 -->
            <Label Text="入库管理" 
                   FontSize="24" 
                   FontAttributes="Bold" 
                   HorizontalOptions="Center" 
                   Margin="0,0,0,20"/>
            
            <!-- 收货模块 -->
            <Frame Padding="15" CornerRadius="10" BackgroundColor="#E3F2FD">
                <VerticalStackLayout Spacing="10">
                    <Label Text="收货管理" FontSize="18" FontAttributes="Bold" TextColor="#1976D2"/>
                    <Grid ColumnDefinitions="*,*" RowDefinitions="Auto,Auto" ColumnSpacing="10" RowSpacing="10">
                        <!-- 收货查询 -->
                        <Button Grid.Row="0" Grid.Column="0" 
                                Text="收货查询" 
                                BackgroundColor="#2196F3" 
                                TextColor="White"
                                Clicked="OnRecQueryClicked"/>
                        <!-- 收货扫描 -->
                        <Button Grid.Row="0" Grid.Column="1" 
                                Text="收货扫描" 
                                BackgroundColor="#2196F3" 
                                TextColor="White"
                                Clicked="OnRecScanClicked"/>
                    </Grid>
                </VerticalStackLayout>
            </Frame>
            
            <!-- 上架模块 -->
            <Frame Padding="15" CornerRadius="10" BackgroundColor="#E8F5E8">
                <VerticalStackLayout Spacing="10">
                    <Label Text="上架管理" FontSize="18" FontAttributes="Bold" TextColor="#388E3C"/>
                    <Grid ColumnDefinitions="*,*" RowDefinitions="Auto,Auto" ColumnSpacing="10" RowSpacing="10">
                        <!-- 上架查询 -->
                        <Button Grid.Row="0" Grid.Column="0" 
                                Text="上架查询" 
                                BackgroundColor="#4CAF50" 
                                TextColor="White"
                                Clicked="OnPkiQueryClicked"/>
                        <!-- 上架扫描 -->
                        <Button Grid.Row="0" Grid.Column="1" 
                                Text="上架扫描" 
                                BackgroundColor="#4CAF50" 
                                TextColor="White"
                                Clicked="OnPkiScanClicked"/>
                    </Grid>
                </VerticalStackLayout>
            </Frame>
            
            <!-- 入库模块 -->
            <Frame Padding="15" CornerRadius="10" BackgroundColor="#FFF3E0">
                <VerticalStackLayout Spacing="10">
                    <Label Text="入库管理" FontSize="18" FontAttributes="Bold" TextColor="#F57C00"/>
                    <Grid ColumnDefinitions="*,*" RowDefinitions="Auto,Auto" ColumnSpacing="10" RowSpacing="10">
                        <!-- 入库查询 -->
                        <Button Grid.Row="0" Grid.Column="0" 
                                Text="入库查询" 
                                BackgroundColor="#FF9800" 
                                TextColor="White"
                                Clicked="OnIpkQueryClicked"/>
                        <!-- 入库扫描 -->
                        <Button Grid.Row="0" Grid.Column="1" 
                                Text="入库扫描" 
                                BackgroundColor="#FF9800" 
                                TextColor="White"
                                Clicked="OnIpkScanClicked"/>
                    </Grid>
                </VerticalStackLayout>
            </Frame>
        </VerticalStackLayout>
    </ScrollView>
</ContentPage>
