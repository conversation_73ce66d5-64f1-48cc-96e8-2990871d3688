using CommunityToolkit.Mvvm.Messaging;
using WMS.APP.Message;
using WMS.MODEL;

namespace WMS.APP.Common
{
    /// <summary>
    /// 扫描管理器 - 统一管理扫描流程和PDA广播
    /// </summary>
    public class ScanManager
    {
        private static ScanManager _instance;
        private static readonly object _lock = new object();
        
        private List<ScanFieldConfig> _currentFlow;
        private Dictionary<string, string> _scanResults;
        private int _currentFieldIndex = 0;
        private string _currentFlowId;

        public static ScanManager Instance
        {
            get
            {
                if (_instance == null)
                {
                    lock (_lock)
                    {
                        if (_instance == null)
                            _instance = new ScanManager();
                    }
                }
                return _instance;
            }
        }

        private ScanManager()
        {
            _scanResults = new Dictionary<string, string>();
            
            // 注册消息监听
            WeakReferenceMessenger.Default.Register<ScanCompletedMessage>(this, OnScanCompleted);
            WeakReferenceMessenger.Default.Register<ScanFocusChangedMessage>(this, OnScanFocusChanged);
        }

        /// <summary>
        /// 开始扫描流程
        /// </summary>
        /// <param name="flowConfig">扫描流程配置</param>
        public void StartScanFlow(ScanFlowConfig flowConfig)
        {
            _currentFlow = flowConfig.Fields.OrderBy(f => f.Order).ToList();
            _currentFlowId = flowConfig.FlowId;
            _scanResults.Clear();
            _currentFieldIndex = 0;

            // 激活第一个字段
            if (_currentFlow.Count > 0)
            {
                ActivateField(_currentFlow[0].FieldId);
            }
        }

        /// <summary>
        /// 重置扫描流程
        /// </summary>
        public void ResetScanFlow()
        {
            _scanResults.Clear();
            _currentFieldIndex = 0;
            
            if (_currentFlow?.Count > 0)
            {
                // 重新激活第一个字段
                ActivateField(_currentFlow[0].FieldId);
                
                // 清空所有字段
                foreach (var field in _currentFlow)
                {
                    WeakReferenceMessenger.Default.Send(new ClearScanFieldMessage(field.FieldId));
                }
            }
        }

        /// <summary>
        /// 获取当前扫描结果
        /// </summary>
        public Dictionary<string, string> GetScanResults()
        {
            return new Dictionary<string, string>(_scanResults);
        }

        /// <summary>
        /// 模拟PDA广播扫描数据
        /// </summary>
        /// <param name="scanData">扫描数据</param>
        /// <param name="deviceId">设备ID</param>
        public void SimulatePdaBroadcast(string scanData, string deviceId = "PDA001")
        {
            WeakReferenceMessenger.Default.Send(new PdaBroadcastMessage(scanData, deviceId));
        }

        private void OnScanCompleted(object recipient, ScanCompletedMessage message)
        {
            // 保存扫描结果
            _scanResults[message.FromControlId] = message.InputResult;

            // 如果是最后一个字段，完成整个流程
            if (message.IsFinalField)
            {
                CompleteScanFlow();
                return;
            }

            // 移动到下一个字段
            MoveToNextField();
        }

        private void OnScanFocusChanged(object recipient, ScanFocusChangedMessage message)
        {
            // 用户手动点击了某个字段，激活该字段
            ActivateField(message.ControlId);
            
            // 更新当前字段索引
            if (_currentFlow != null)
            {
                var fieldIndex = _currentFlow.FindIndex(f => f.FieldId == message.ControlId);
                if (fieldIndex >= 0)
                {
                    _currentFieldIndex = fieldIndex;
                }
            }
        }

        private void MoveToNextField()
        {
            if (_currentFlow == null || _currentFieldIndex >= _currentFlow.Count - 1)
                return;

            // 取消当前字段激活
            if (_currentFieldIndex >= 0 && _currentFieldIndex < _currentFlow.Count)
            {
                DeactivateField(_currentFlow[_currentFieldIndex].FieldId);
            }

            // 激活下一个字段
            _currentFieldIndex++;
            if (_currentFieldIndex < _currentFlow.Count)
            {
                ActivateField(_currentFlow[_currentFieldIndex].FieldId);
            }
        }

        private void ActivateField(string fieldId)
        {
            WeakReferenceMessenger.Default.Send(new ActivateScanFieldMessage(fieldId));
        }

        private void DeactivateField(string fieldId)
        {
            WeakReferenceMessenger.Default.Send(new DeactivateScanFieldMessage(fieldId));
        }

        private void CompleteScanFlow()
        {
            // 取消所有字段激活
            if (_currentFlow != null)
            {
                foreach (var field in _currentFlow)
                {
                    DeactivateField(field.FieldId);
                }
            }

            // 发送流程完成消息
            WeakReferenceMessenger.Default.Send(new ScanFlowCompletedMessage(_currentFlowId, _scanResults));
        }
    }

    /// <summary>
    /// 激活扫描字段消息
    /// </summary>
    public class ActivateScanFieldMessage
    {
        public string FieldId { get; }
        
        public ActivateScanFieldMessage(string fieldId)
        {
            FieldId = fieldId;
        }
    }

    /// <summary>
    /// 取消激活扫描字段消息
    /// </summary>
    public class DeactivateScanFieldMessage
    {
        public string FieldId { get; }
        
        public DeactivateScanFieldMessage(string fieldId)
        {
            FieldId = fieldId;
        }
    }

    /// <summary>
    /// 清空扫描字段消息
    /// </summary>
    public class ClearScanFieldMessage
    {
        public string FieldId { get; }
        
        public ClearScanFieldMessage(string fieldId)
        {
            FieldId = fieldId;
        }
    }
}
