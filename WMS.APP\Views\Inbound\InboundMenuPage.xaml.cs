namespace WMS.APP.Views.Inbound;

public partial class InboundMenuPage : ContentPage
{
    public InboundMenuPage()
    {
        InitializeComponent();
    }

    private async void OnRecQueryClicked(object sender, EventArgs e)
    {
        // 导航到收货查询页面
        await Shell.Current.GoToAsync("//RecQueryPage");
    }

    private async void OnRecScanClicked(object sender, EventArgs e)
    {
        // 导航到收货扫描页面
        await Shell.Current.GoToAsync("//RecScanPage");
    }

    private async void OnPkiQueryClicked(object sender, EventArgs e)
    {
        // 导航到上架查询页面
        await Shell.Current.GoToAsync("//PkiQueryPage");
    }

    private async void OnPkiScanClicked(object sender, EventArgs e)
    {
        // 导航到上架扫描页面
        await Shell.Current.GoToAsync("//PkiScanPage");
    }

    private async void OnIpkQueryClicked(object sender, EventArgs e)
    {
        // 导航到入库查询页面
        await Shell.Current.GoToAsync("//IpkQueryPage");
    }

    private async void OnIpkScanClicked(object sender, EventArgs e)
    {
        // 导航到入库扫描页面
        await Shell.Current.GoToAsync("//IpkScanPage");
    }
}
