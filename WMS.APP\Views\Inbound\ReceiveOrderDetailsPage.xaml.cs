using WMS.APP.ViewModels.inbound;
using WMS.MODEL;
using WMS.SERVICE;

namespace WMS.APP.Views.inbound
{
    //[QueryProperty(nameof(OrderId), "orderId")]
    //[QueryProperty(nameof(OrderId), "orderId")]
    //[QueryProperty(nameof(OrderParameter), "param")]
    public partial class ReceiveOrderDetailsPage : ContentPage, IQueryAttributable
    {
        private readonly ReceiveOrderDetailsViewModel _viewModel;
        private readonly IIboundService _dataService;

        public ReceiveOrderDetailsPage(ReceiveOrderDetailsViewModel viewModel, IIboundService dataService)
        {
            InitializeComponent();
            _viewModel = viewModel;
            _dataService = dataService;
            BindingContext = _viewModel;
        }

        public void ApplyQueryAttributes(IDictionary<string, object> query)
        {
            // ✅ 打印所有传入的参数键和值
            foreach (var key in query.Keys)
            {
                var value = query[key];
                System.Diagnostics.Debug.WriteLine($"[DetailPage] 参数 {key}: {value} ({value?.GetType().Name})");
            }

            // ✅ 使用某个具体参数
            if (query.TryGetValue("param", out var obj) && obj is ReceiveOrderModel orderModel)
            {
                // 将对象绑定到 UI
                //BindingContext = person;
                _viewModel?.Initialize(orderModel);
            }
        }

        private string _orderId;
        public string OrderId
        {
            get => _orderId;
            set
            {
                _orderId = value;
                LoadOrder();
            }
        }

        private async void LoadOrder()
        {
            if (!string.IsNullOrEmpty(_orderId))
            {
                var order = await _dataService.GetReceiveOrderByIdAsync(_orderId);
                if (order != null)
                {
                    //_viewModel.Initialize(order);
                }
            }
        }

        protected override void OnNavigatedTo(NavigatedToEventArgs args)
        {
            base.OnNavigatedTo(args);
            // 不再尝试从 args.Parameter 获取参数
        }
    }
}

