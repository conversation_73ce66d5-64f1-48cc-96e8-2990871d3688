using Microsoft.Maui.Controls;
using WMS.APP.ViewModels.inbound;
using WMS.APP.Common;
using CommunityToolkit.Mvvm.Messaging;
using WMS.APP.Message;

namespace WMS.APP.Views.inbound
{
    public partial class IpkScanPage : ContentPage
    {
        private readonly IpkScanViewModel _viewModel;

        public IpkScanPage(IpkScanViewModel viewModel)
        {
            InitializeComponent();
            _viewModel = viewModel;
            BindingContext = _viewModel;
            
            // 注册消息接收器
            WeakReferenceMessenger.Default.Register<ScanCompletedMessage>(this, OnScanCompleted);
        }

        private void OnScanCompleted(object recipient, ScanCompletedMessage message)
        {
            // 将消息转发给ViewModel处理
            _viewModel.HandleScanResult(message);
        }

        protected override void OnDisappearing()
        {
            base.OnDisappearing();
            // 取消注册消息接收器
            WeakReferenceMessenger.Default.Unregister<ScanCompletedMessage>(this);
        }
    }
}