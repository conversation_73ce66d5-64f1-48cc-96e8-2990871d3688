using CommunityToolkit.Mvvm.ComponentModel;
using CommunityToolkit.Mvvm.Input;
using System.Collections.ObjectModel;
using WMS.MODEL;
using WMS.SERVICE;
using WMS.APP.Common;
using WMS.APP.Views.inbound;

namespace WMS.APP.ViewModels.inbound
{
    public partial class IpkListViewModel : ObservableObject
    {
        private readonly IIboundService _dataService;
        private readonly INavigationService _navigationService;

        [ObservableProperty]
        private string searchOrderNumber;

        [ObservableProperty]
        private string searchCustomerOrderNumber;

        [ObservableProperty]
        private ObservableCollection<ReceiveOrderModel> orders = new();

        // 构造函数通过依赖注入方式获取服务
        public IpkListViewModel(IIboundService dataService, INavigationService navigationService)
        {
            _dataService = dataService;
            _navigationService = navigationService;
            _ = LoadOrdersAsync();
        }

        [RelayCommand]
        private async Task Search()
        {
            await LoadOrdersAsync();
        }

        private async Task LoadOrdersAsync()
        {
            var keyword = string.IsNullOrWhiteSpace(SearchOrderNumber) ? null : SearchOrderNumber;
            var customerOrder = string.IsNullOrWhiteSpace(SearchCustomerOrderNumber) ? null : SearchCustomerOrderNumber;

            var allOrders = await _dataService.GetReceiveOrdersAsync();

            // 过滤出可以拣货的订单（例如状态为"已确认"的订单）
            var filtered = allOrders.Where(o =>
                (string.IsNullOrEmpty(keyword) || (o.OrderNumber?.Contains(keyword) ?? false)) &&
                (string.IsNullOrEmpty(customerOrder) || (o.Supplier?.Contains(customerOrder) ?? false)) &&
                (o.Status == "已确认" || o.Status == "部分拣货") // 添加适合拣货的状态过滤
            ).ToList();

            Orders.Clear();
            foreach (var order in filtered)
                Orders.Add(order);
        }

        [RelayCommand]
        private async Task GoPick(ReceiveOrderModel order)
        {
            if (order != null)
            {
                await _navigationService.NavigateToAsync($"{nameof(IpkScanPage)}?orderId={order.Id}");
            }
        }
    }
}