﻿using System.Collections.ObjectModel;
using System.ComponentModel;
using System.Runtime.CompilerServices;
using System.Runtime.Versioning;
using System.Windows.Input;
using WMS.APP.Views.inbound;
using WMS.APP.Views.inventory;
using WMS.APP.Views.Outbound;
using WMS.MODEL;

namespace WMS.APP.ViewModels
{
    public class MenuViewModel : INotifyPropertyChanged
    {
        public ObservableCollection<MenuItemModel> MenuItems { get; } = new();

        private readonly INavigation _navigation;

        //private readonly IServiceProvider _serviceProvider;

        // 导航命令
        public ICommand NavigateCommand { get; }

        public MenuViewModel()
        {
            //_navigation = navigation;
            //_serviceProvider = serviceProvider;
            // 初始化菜单项
            LoadMenuItems();

            //NavigateCommand = new Command<Type>(NavigateToPage);
            // 初始化命令
            NavigateCommand = new Command<Type>(async (pageType) =>
            {
                if (pageType != null)
                {
                    //var page = App.ServiceProvider.GetRequiredService<pageType>();
                    var page = (Page)App.ServiceProvider.GetRequiredService(pageType);
                    //await _navigation.PushModalAsync(page);

                    //var page = (Page?)Activator.CreateInstance(pageType);
                    if (page != null)
                    {
                        await Shell.Current.Navigation.PushAsync(page);
                    }
                    //var page = (Page)Activator.CreateInstance(pageType);
                    //await Shell.Current.Navigation.PushAsync(page);
                }
            });
        }

        private async void NavigateToPage(Type pageType)
        {

            // 通过依赖注入容器创建实例（自动处理构造函数参数）
            var page = (Page)App.ServiceProvider.GetRequiredService(pageType);
            await Shell.Current.Navigation.PushAsync(page);
        }


        /// <summary>
        /// 动态判断显示权限，并选择图标
        /// </summary>
        private void LoadMenuItems()
        {
            /*MenuItems.Add(new MenuItemModel
            {
                Title = "收货查询",
                Icon = "dotnet_bot.png",
                TargetPageType = typeof(ReceiveQueryPage)
            });
            MenuItems.Add(new MenuItemModel
            {
                Title = "收货查询",
                Icon = "dotnet_bot.png",
                TargetPageType = typeof(ReceivePage)
            });

            MenuItems.Add(new MenuItemModel
            {
                Title = "收货扫描",
                Icon = "dotnet_bot.png",
                TargetPageType = typeof(RecScanPage)
            });

            MenuItems.Add(new MenuItemModel
            {
                Title = "上架扫描",
                Icon = "dotnet_bot.png",
                TargetPageType = typeof(IPKScanPage)
            });

            

            MenuItems.Add(new MenuItemModel
            {
                Title = "库存查询",
                Icon = "dotnet_bot.png",
                TargetPageType = typeof(ScanPage)
            });

            MenuItems.Add(new MenuItemModel
            {
                Title = "移库扫描",
                Icon = "dotnet_bot.png",
                TargetPageType = typeof(View.ScanTestPage)
            });

            MenuItems.Add(new MenuItemModel
            {
                Title = "分拣扫描",
                Icon = "dotnet_bot.png",
                TargetPageType = typeof(PkmQueryPage)
            });

            MenuItems.Add(new MenuItemModel
            {
                Title = "创建订单",
                Icon = "dotnet_bot.png",
                TargetPageType = typeof(OutboundCreatePage)
            });*/

            MenuItems.Add(new MenuItemModel
            {
                Title = "仓库收货",
                Icon = "dotnet_bot.png",
                TargetPageType = typeof(InboundListPage)
            });

            MenuItems.Add(new MenuItemModel
            {
                Title = "分拣扫描",
                Icon = "dotnet_bot.png",
                TargetPageType = typeof(PkmScanPage)
            });

            MenuItems.Add(new MenuItemModel
            {
                Title = "收货单录入",
                Icon = "dotnet_bot.png",
                TargetPageType = typeof(ReceiveOrderListPage)
            });

            MenuItems.Add(new MenuItemModel
            {
                Title = "移库操作",
                Icon = "dotnet_bot.png",
                TargetPageType = typeof(InvTransferPage)
            });

            MenuItems.Add(new MenuItemModel
            {
                Title = "上架列表",
                Icon = "dotnet_bot.png",
                TargetPageType = typeof(IpkListPage)
            });

            MenuItems.Add(new MenuItemModel
            {
                Title = "上架扫描",
                Icon = "dotnet_bot.png",
                TargetPageType = typeof(IpkScanPage)
            });
            // 添加更多菜单项...
        }

        // INotifyPropertyChanged
        public event PropertyChangedEventHandler? PropertyChanged;

        protected virtual void OnPropertyChanged([CallerMemberName] string propertyName = null)
        {
            PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
        }
    }
}
