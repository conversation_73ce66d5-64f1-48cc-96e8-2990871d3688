using System.ComponentModel;
using System.Runtime.CompilerServices;
using System.Windows.Input;
using CommunityToolkit.Mvvm.Messaging;
using WMS.APP.Common;
using WMS.APP.Message;
using WMS.MODEL;
using WMS.SERVICE;

namespace WMS.APP.ViewModels.Inbound
{
    public class RecScanViewModel : INotifyPropertyChanged
    {
        private readonly IInboundService _inboundService;
        private readonly ScanManager _scanManager;
        
        // 扫描字段
        private string _ord = string.Empty;
        private string _skucod = string.Empty;
        private string _loc = string.Empty;
        private string _qty = string.Empty;
        
        // 状态字段
        private bool _isOrdActive = true;
        private bool _isSkucodActive = false;
        private bool _isLocActive = false;
        private bool _isQtyActive = false;
        
        // 业务对象
        private ReceiveOrderModel _currentRecord;
        private RecconModel _currentProduct;
        
        // UI状态
        private string _statusMessage = string.Empty;
        private Color _statusMessageColor = Colors.Black;
        private bool _showStatusMessage = false;
        private bool _showOrderInfo = false;
        private bool _showProductInfo = false;
        private bool _showTestArea = true; // 开发阶段显示测试区域
        private string _testScanData = string.Empty;

        public RecScanViewModel(IInboundService inboundService)
        {
            _inboundService = inboundService;
            _scanManager = ScanManager.Instance;
            
            InitializeCommands();
            RegisterMessages();
            InitializeScanFlow();
        }

        #region Properties

        public string ORD
        {
            get => _ord;
            set
            {
                _ord = value;
                OnPropertyChanged();
            }
        }

        public string SKUCOD
        {
            get => _skucod;
            set
            {
                _skucod = value;
                OnPropertyChanged();
            }
        }

        public string LOC
        {
            get => _loc;
            set
            {
                _loc = value;
                OnPropertyChanged();
            }
        }

        public string QTY
        {
            get => _qty;
            set
            {
                _qty = value;
                OnPropertyChanged();
                OnPropertyChanged(nameof(CanSubmit));
            }
        }

        public bool IsOrdActive
        {
            get => _isOrdActive;
            set
            {
                _isOrdActive = value;
                OnPropertyChanged();
            }
        }

        public bool IsSkucodActive
        {
            get => _isSkucodActive;
            set
            {
                _isSkucodActive = value;
                OnPropertyChanged();
            }
        }

        public bool IsLocActive
        {
            get => _isLocActive;
            set
            {
                _isLocActive = value;
                OnPropertyChanged();
            }
        }

        public bool IsQtyActive
        {
            get => _isQtyActive;
            set
            {
                _isQtyActive = value;
                OnPropertyChanged();
            }
        }

        public ReceiveOrderModel CurrentRecord
        {
            get => _currentRecord;
            set
            {
                _currentRecord = value;
                OnPropertyChanged();
                OnPropertyChanged(nameof(StatusColor));
                ShowOrderInfo = value != null;
            }
        }

        public RecconModel CurrentProduct
        {
            get => _currentProduct;
            set
            {
                _currentProduct = value;
                OnPropertyChanged();
                OnPropertyChanged(nameof(RemainingQuantity));
                OnPropertyChanged(nameof(RemainingQuantityColor));
                ShowProductInfo = value != null;
            }
        }

        public string StatusMessage
        {
            get => _statusMessage;
            set
            {
                _statusMessage = value;
                OnPropertyChanged();
                ShowStatusMessage = !string.IsNullOrEmpty(value);
            }
        }

        public Color StatusMessageColor
        {
            get => _statusMessageColor;
            set
            {
                _statusMessageColor = value;
                OnPropertyChanged();
            }
        }

        public bool ShowStatusMessage
        {
            get => _showStatusMessage;
            set
            {
                _showStatusMessage = value;
                OnPropertyChanged();
            }
        }

        public bool ShowOrderInfo
        {
            get => _showOrderInfo;
            set
            {
                _showOrderInfo = value;
                OnPropertyChanged();
            }
        }

        public bool ShowProductInfo
        {
            get => _showProductInfo;
            set
            {
                _showProductInfo = value;
                OnPropertyChanged();
            }
        }

        public bool ShowTestArea
        {
            get => _showTestArea;
            set
            {
                _showTestArea = value;
                OnPropertyChanged();
            }
        }

        public string TestScanData
        {
            get => _testScanData;
            set
            {
                _testScanData = value;
                OnPropertyChanged();
            }
        }

        public Color StatusColor => CurrentRecord?.Status switch
        {
            "待收货" => Colors.Orange,
            "收货中" => Colors.Blue,
            "已完成" => Colors.Green,
            _ => Colors.Gray
        };

        public decimal RemainingQuantity => CurrentProduct != null ? CurrentProduct.QTY - CurrentProduct.ActualQuantity : 0;

        public Color RemainingQuantityColor => RemainingQuantity > 0 ? Colors.Orange : Colors.Green;

        public bool CanSubmit => !string.IsNullOrEmpty(ORD) && !string.IsNullOrEmpty(SKUCOD) && 
                                !string.IsNullOrEmpty(LOC) && !string.IsNullOrEmpty(QTY);

        public bool CanComplete => CurrentRecord != null && CurrentRecord.Status != "已完成";

        #endregion

        #region Commands

        public ICommand SubmitCommand { get; private set; }
        public ICommand ResetCommand { get; private set; }
        public ICommand CompleteCommand { get; private set; }
        public ICommand SimulateScanCommand { get; private set; }

        private void InitializeCommands()
        {
            SubmitCommand = new Command(async () => await SubmitScan());
            ResetCommand = new Command(ResetScan);
            CompleteCommand = new Command(async () => await CompleteScan());
            SimulateScanCommand = new Command(SimulateScan);
        }

        #endregion

        #region Methods

        private void RegisterMessages()
        {
            WeakReferenceMessenger.Default.Register<ScanFlowCompletedMessage>(this, OnScanFlowCompleted);
        }

        private void InitializeScanFlow()
        {
            var scanFlow = new ScanFlowConfig
            {
                FlowId = "REC_SCAN",
                FlowName = "收货扫描",
                Fields = new List<ScanFieldConfig>
                {
                    new ScanFieldConfig { FieldId = "ORD", Title = "系统订单号", Order = 1, IsRequired = true },
                    new ScanFieldConfig { FieldId = "SKUCOD", Title = "商品编号", Order = 2, IsRequired = true },
                    new ScanFieldConfig { FieldId = "LOC", Title = "库位号", Order = 3, IsRequired = true },
                    new ScanFieldConfig { FieldId = "QTY", Title = "收货数量", Order = 4, IsRequired = true, IsFinalManualEntry = true }
                }
            };

            _scanManager.StartScanFlow(scanFlow);
        }

        private async void OnScanFlowCompleted(object recipient, ScanFlowCompletedMessage message)
        {
            if (message.FlowId == "REC_SCAN")
            {
                var results = message.ScanResults;
                ORD = results.GetValueOrDefault("ORD", "");
                SKUCOD = results.GetValueOrDefault("SKUCOD", "");
                LOC = results.GetValueOrDefault("LOC", "");
                QTY = results.GetValueOrDefault("QTY", "");

                await SubmitScan();
            }
        }

        private async Task SubmitScan()
        {
            try
            {
                if (!CanSubmit)
                {
                    ShowMessage("请完成所有必填字段的扫描", Colors.Red);
                    return;
                }

                // 验证数量格式
                if (!decimal.TryParse(QTY, out decimal quantity) || quantity <= 0)
                {
                    ShowMessage("请输入有效的数量", Colors.Red);
                    return;
                }

                ShowMessage("正在处理收货扫描...", Colors.Blue);

                // 调用服务处理扫描
                var result = await _inboundService.ProcessRecScanAsync(ORD, SKUCOD, LOC, quantity);

                if (result.IsSuccess)
                {
                    ShowMessage(result.Message, Colors.Green);

                    // 更新当前订单和商品信息
                    await LoadOrderInfo();
                    await LoadProductInfo();

                    // 重置扫描流程
                    ResetScan();
                }
                else
                {
                    ShowMessage(result.Message, Colors.Red);
                }
            }
            catch (Exception ex)
            {
                ShowMessage($"提交失败: {ex.Message}", Colors.Red);
            }
        }

        private void ResetScan()
        {
            ORD = string.Empty;
            SKUCOD = string.Empty;
            LOC = string.Empty;
            QTY = string.Empty;

            // 重新开始扫描流程
            _scanManager.ResetScanFlow();

            ShowMessage("已重置扫描", Colors.Gray);
        }

        private async Task CompleteScan()
        {
            try
            {
                if (CurrentRecord == null)
                {
                    ShowMessage("请先扫描订单", Colors.Red);
                    return;
                }

                ShowMessage("正在完成收货...", Colors.Blue);

                var result = await _inboundService.CompleteRecScanAsync(CurrentRecord.Id);

                if (result)
                {
                    ShowMessage("收货完成！", Colors.Green);
                    CurrentRecord.Status = "已完成";
                    OnPropertyChanged(nameof(CanComplete));
                    OnPropertyChanged(nameof(StatusColor));
                }
                else
                {
                    ShowMessage("完成收货失败", Colors.Red);
                }
            }
            catch (Exception ex)
            {
                ShowMessage($"完成收货失败: {ex.Message}", Colors.Red);
            }
        }

        private void SimulateScan()
        {
            if (!string.IsNullOrEmpty(TestScanData))
            {
                _scanManager.SimulatePdaBroadcast(TestScanData);
                TestScanData = string.Empty;
            }
        }

        private async Task LoadOrderInfo()
        {
            try
            {
                if (!string.IsNullOrEmpty(ORD))
                {
                    var record = await _inboundService.GetRecordByOrderAsync(ORD);
                    CurrentRecord = record;
                }
            }
            catch (Exception ex)
            {
                ShowMessage($"加载订单信息失败: {ex.Message}", Colors.Red);
            }
        }

        private async Task LoadProductInfo()
        {
            try
            {
                if (CurrentRecord != null && !string.IsNullOrEmpty(SKUCOD))
                {
                    var reccons = await _inboundService.GetRecconListAsync(CurrentRecord.Id);
                    CurrentProduct = reccons.FirstOrDefault(r => r.SKUCOD == SKUCOD);
                }
            }
            catch (Exception ex)
            {
                ShowMessage($"加载商品信息失败: {ex.Message}", Colors.Red);
            }
        }

        private void ShowMessage(string message, Color color)
        {
            StatusMessage = message;
            StatusMessageColor = color;

            // 3秒后自动清除消息
            _ = Task.Delay(3000).ContinueWith(_ =>
            {
                MainThread.BeginInvokeOnMainThread(() =>
                {
                    if (StatusMessage == message) // 确保没有被新消息覆盖
                    {
                        StatusMessage = string.Empty;
                    }
                });
            });
        }

        #endregion

        public event PropertyChangedEventHandler PropertyChanged;

        protected virtual void OnPropertyChanged([CallerMemberName] string propertyName = null)
        {
            PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
        }
    }
}
