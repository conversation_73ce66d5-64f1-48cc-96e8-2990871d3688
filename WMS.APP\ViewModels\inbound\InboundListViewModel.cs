using CommunityToolkit.Mvvm.ComponentModel;
using CommunityToolkit.Mvvm.Input;
using System.Collections.ObjectModel;
using WMS.MODEL;
using WMS.SERVICE;
using WMS.APP.Common;
using WMS.APP.Views.inbound;

namespace WMS.APP.ViewModels.inbound
{
    public partial class InboundListViewModel : ObservableObject
    {
        private readonly IInboundService _dataService;
        private readonly INavigationService _navigationService;

        [ObservableProperty]
        private string searchOrderNumber;

        [ObservableProperty]
        private string searchCustomerOrderNumber;

        [ObservableProperty]
        private ObservableCollection<ReceiveOrderModel> orders = new();

        // ���캯��ͨ������ע�뷽ʽ�������
        public InboundListViewModel(IInboundService dataService, INavigationService navigationService)
        {
            _dataService = dataService;
            _navigationService = navigationService;
            _ = LoadOrdersAsync();
        }

        [RelayCommand]
        private async Task Search()
        {
            await LoadOrdersAsync();
        }

        private async Task LoadOrdersAsync()
        {
            var keyword = string.IsNullOrWhiteSpace(SearchOrderNumber) ? null : SearchOrderNumber;
            var customerOrder = string.IsNullOrWhiteSpace(SearchCustomerOrderNumber) ? null : SearchCustomerOrderNumber;

            var allOrders = await _dataService.GetReceiveOrdersAsync();

            var filtered = allOrders.Where(o =>
                (string.IsNullOrEmpty(keyword) || (o.OrderNumber?.Contains(keyword) ?? false)) &&
                (string.IsNullOrEmpty(customerOrder) || (o.Supplier?.Contains(customerOrder) ?? false))
            ).ToList();

            Orders.Clear();
            foreach (var order in filtered)
                Orders.Add(order);
        }

        [RelayCommand]
        private async Task GoReceive(ReceiveOrderModel order)
        {
            if (order != null)
            {
                await _navigationService.NavigateToAsync($"{nameof(ReceiveOrderDetailsPage)}?orderId={order.Id}");
            }
        }
    }
}