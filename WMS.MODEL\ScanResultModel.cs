namespace WMS.MODEL
{
    /// <summary>
    /// 扫描结果模型
    /// </summary>
    public class ScanResultModel
    {
        public bool IsSuccess { get; set; }
        public string Message { get; set; }
        public string ScannedValue { get; set; }
        public string NextFieldId { get; set; }
        public Dictionary<string, object> Data { get; set; } = new();
    }
    
    /// <summary>
    /// 扫描字段配置模型
    /// </summary>
    public class ScanFieldConfig
    {
        public string FieldId { get; set; }
        public string Title { get; set; }
        public bool IsRequired { get; set; } = true;
        public bool IsFinalManualEntry { get; set; } = false;
        public string ValidationPattern { get; set; }
        public string ErrorMessage { get; set; }
        public int Order { get; set; }
    }
    
    /// <summary>
    /// 扫描流程配置模型
    /// </summary>
    public class ScanFlowConfig
    {
        public string FlowId { get; set; }
        public string FlowName { get; set; }
        public List<ScanFieldConfig> Fields { get; set; } = new();
        public string SubmitApiUrl { get; set; }
    }
}
