﻿using WMS.APP.Views.inbound; // 确保已引用页面命名空间

namespace WMS.APP
{
    public partial class AppShell : Shell
    {
        public AppShell()
        {
            InitializeComponent();

            // 注册 ReceiveOrderEditPage 路由
            Routing.RegisterRoute("ReceiveOrderEditPage", typeof(ReceiveOrderEditPage));

            Routing.RegisterRoute("ReceiveOrderDetailsPage", typeof(ReceiveOrderDetailsPage));
            
            // 注册新增的IPK页面路由
            Routing.RegisterRoute("IpkListPage", typeof(IpkListPage));
            Routing.RegisterRoute("IpkScanPage", typeof(IpkScanPage));
        }
    }
}
