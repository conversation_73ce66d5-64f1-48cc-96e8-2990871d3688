﻿using CommunityToolkit.Maui;
using CommunityToolkit.Maui.Core;
using Microsoft.Extensions.Logging;
using WMS.APP.Common;
using WMS.APP.ViewModel;
using WMS.APP.ViewModels;
using WMS.APP.ViewModels.inbound;
using WMS.APP.ViewModels.Inbound;
using WMS.APP.ViewModels.Inventory;
using WMS.APP.ViewModels.Outbound;
using WMS.APP.ViewModels.system;
using WMS.APP.Views.inbound;
using WMS.APP.Views.Inbound;
using WMS.APP.Views.Inventory;
using WMS.APP.Views.Outbound;
using WMS.APP.Views.system;
using WMS.SERVICE;

namespace WMS.APP
{
    public static class MauiProgram
    {
        public static MauiApp CreateMauiApp()
        {
            var builder = MauiApp.CreateBuilder();
#if ANDROID
            WMS.APP.Platforms.Android.EntryBehavior.Init();
            //MauiApp.Platforms.Android.KeyboardWatcher.Init();
#endif

            builder
                .UseMauiApp<App>()
                .UseMauiCommunityToolkit() // 使用 CommunityToolkit.Maui 扩展方法
                .UseMauiCommunityToolkitCore() // 使用 CommunityToolkit.Maui.Core 扩展方法
                .ConfigureFonts(fonts =>
                {
                    fonts.AddFont("OpenSans-Regular.ttf", "OpenSansRegular");
                    fonts.AddFont("OpenSans-Semibold.ttf", "OpenSansSemibold");
                });

            // Register services
            builder.Services.AddSingleton<INavigationService, NavigationService>();
            builder.Services.AddScoped<IUserService, UserService>();
            builder.Services.AddScoped<IInboundService, InboundService>();
            builder.Services.AddScoped<IOutboundService, OutboundService>();
            builder.Services.AddScoped<IInventoryService, InventoryService>();

            // Register ViewModels
            builder.Services.AddTransient<LoginViewModel>();
            builder.Services.AddTransient<MenuViewModel>();
            builder.Services.AddTransient<ProfileViewModel>();

            // Inbound ViewModels
            builder.Services.AddTransient<RecQueryViewModel>();
            builder.Services.AddTransient<RecScanViewModel>();
            builder.Services.AddTransient<ReceiveOrderViewModel>();
            builder.Services.AddTransient<ReceiveOrderDetailsViewModel>();
            builder.Services.AddTransient<ReceiveOrderEditViewModel>();
            builder.Services.AddTransient<InboundListViewModel>();
            builder.Services.AddTransient<IpkListViewModel>();
            builder.Services.AddTransient<IpkScanViewModel>();

            // Outbound ViewModels
            builder.Services.AddTransient<PkmQueryViewModel>();
            builder.Services.AddTransient<PkmScanViewModel>();

            // Inventory ViewModels
            builder.Services.AddTransient<InvQueryViewModel>();
            builder.Services.AddTransient<InvTransferViewModel>();

            // Register Views
            builder.Services.AddTransient<LoginPage>();
            builder.Services.AddTransient<MainPage>();
            builder.Services.AddTransient<MenuPage>();
            builder.Services.AddTransient<ProfilePage>();

            // Menu Pages
            builder.Services.AddTransient<InboundMenuPage>();
            builder.Services.AddTransient<OutboundMenuPage>();
            builder.Services.AddTransient<InventoryMenuPage>();

            // Inbound Pages
            builder.Services.AddTransient<RecQueryPage>();
            builder.Services.AddTransient<RecScanPage>();
            builder.Services.AddTransient<ReceiveOrderListPage>();
            builder.Services.AddTransient<ReceiveOrderDetailsPage>();
            builder.Services.AddTransient<ReceiveOrderEditPage>();
            builder.Services.AddTransient<InboundListPage>();
            builder.Services.AddTransient<IpkListPage>();
            builder.Services.AddTransient<IpkScanPage>();

            // Outbound Pages
            builder.Services.AddTransient<PkmQueryPage>();
            builder.Services.AddTransient<PkmScanPage>();

            // Inventory Pages
            builder.Services.AddTransient<InvQueryPage>();
            builder.Services.AddTransient<InvTransferPage>();

#if DEBUG
            builder.Logging.AddDebug();
#endif

            return builder.Build();
        }
    }
}
