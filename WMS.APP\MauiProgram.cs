﻿using CommunityToolkit.Maui;
using CommunityToolkit.Maui.Core;
using Microsoft.Extensions.Logging;
using WMS.APP.Common;
using WMS.APP.ViewModel;
using WMS.APP.ViewModels;
using WMS.APP.ViewModels.inbound;
using WMS.APP.ViewModels.inventory;
using WMS.APP.ViewModels.outbound;
using WMS.APP.ViewModels.system;
using WMS.APP.Views.inbound;
using WMS.APP.Views.inventory;
using WMS.APP.Views.outbound;
using WMS.APP.Views.system;
using WMS.SERVICE;

namespace WMS.APP
{
    public static class MauiProgram
    {
        public static MauiApp CreateMauiApp()
        {
            var builder = MauiApp.CreateBuilder();
#if ANDROID
            WMS.APP.Platforms.Android.EntryBehavior.Init();
            //MauiApp.Platforms.Android.KeyboardWatcher.Init();
#endif

            builder
                .UseMauiApp<App>()
                .UseMauiCommunityToolkit() // 使用 CommunityToolkit.Maui 扩展方法
                .UseMauiCommunityToolkitCore() // 使用 CommunityToolkit.Maui.Core 扩展方法
                .ConfigureFonts(fonts =>
                {
                    fonts.AddFont("OpenSans-Regular.ttf", "OpenSansRegular");
                    fonts.AddFont("OpenSans-Semibold.ttf", "OpenSansSemibold");
                });

            // Register services
            builder.Services.AddSingleton<INavigationService, NavigationService>();
            builder.Services.AddScoped<IUserService, UserService>(); // Ensure WMS.SERVICE is referenced in the project  
            builder.Services.AddScoped<IIboundService, IboundService>();
            builder.Services.AddScoped<IInventoryService, InventoryService>();

            // Register ViewModels
            builder.Services.AddTransient<LoginViewModel>();
            builder.Services.AddTransient<MenuViewModel>();
            builder.Services.AddTransient<ProfileViewModel>();
            builder.Services.AddTransient<ReceiveOrderViewModel>();
            builder.Services.AddTransient<ReceiveOrderDetailsViewModel>();
            builder.Services.AddTransient<ReceiveOrderEditViewModel>();
            builder.Services.AddTransient<PkmScanViewModel>();
            builder.Services.AddTransient<InvTransferViewModel>();
            builder.Services.AddTransient<InboundListViewModel>();
            // 注册新增的ViewModel
            builder.Services.AddTransient<IpkListViewModel>();
            builder.Services.AddTransient<IpkScanViewModel>();

            //register Views
            builder.Services.AddTransient<LoginPage>();
            builder.Services.AddTransient<MainPage>();
            builder.Services.AddTransient<MenuPage>();
            builder.Services.AddTransient<ProfilePage>();
            builder.Services.AddTransient<ReceiveOrderListPage>();
            builder.Services.AddTransient<ReceiveOrderDetailsPage>();
            builder.Services.AddTransient<ReceiveOrderEditPage>();
            builder.Services.AddTransient<PkmScanPage>();
            builder.Services.AddTransient<InvTransferPage>();
            builder.Services.AddTransient<InboundListPage>();
            // 注册新增的View
            builder.Services.AddTransient<IpkListPage>();
            builder.Services.AddTransient<IpkScanPage>();

#if DEBUG
            builder.Logging.AddDebug();
#endif

            return builder.Build();
        }
    }
}
