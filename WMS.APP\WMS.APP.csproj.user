﻿<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="Current" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <PropertyGroup>
    <IsFirstTimeProjectOpen>False</IsFirstTimeProjectOpen>
    <ActiveDebugFramework>net8.0-android</ActiveDebugFramework>
    <ActiveDebugProfile>Pixel 7 - API 34 (Android 14.0 - API 34)</ActiveDebugProfile>
    <SelectedPlatformGroup>Emulator</SelectedPlatformGroup>
    <DefaultDevice>pixel_7_-_api_34</DefaultDevice>
  </PropertyGroup>
  <PropertyGroup Condition="'$(TargetPlatformIdentifier)'=='iOS'">
    <RuntimeIdentifier>ios-arm64</RuntimeIdentifier>
    <PlatformTarget>arm64</PlatformTarget>
  </PropertyGroup>
  <ItemGroup>
    <None Update="App.xaml">
      <SubType>Designer</SubType>
    </None>
    <None Update="AppShell.xaml">
      <SubType>Designer</SubType>
    </None>
    <None Update="MainPage.xaml">
      <SubType>Designer</SubType>
    </None>
    <None Update="Platforms\Windows\App.xaml">
      <SubType>Designer</SubType>
    </None>
    <None Update="Platforms\Windows\Package.appxmanifest">
      <SubType>Designer</SubType>
    </None>
    <None Update="Resources\Styles\Colors.xaml">
      <SubType>Designer</SubType>
    </None>
    <None Update="Resources\Styles\Styles.xaml">
      <SubType>Designer</SubType>
    </None>
  </ItemGroup>
</Project>