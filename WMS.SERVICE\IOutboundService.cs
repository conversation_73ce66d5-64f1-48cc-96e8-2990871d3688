using WMS.MODEL;

namespace WMS.SERVICE
{
    /// <summary>
    /// 出库服务接口
    /// </summary>
    public interface IOutboundService
    {
        #region 分拣管理 (PKM)
        
        // 分拣单主表操作
        Task<List<PkmModel>> GetPkmListAsync(string keyword = null, int page = 1, int pageSize = 20);
        Task<PkmModel> GetPkmByIdAsync(string id);
        Task<PkmModel> GetPkmByOrderAsync(string ord, string con = null);
        Task<bool> SavePkmAsync(PkmModel pkm);
        Task<bool> DeletePkmAsync(string id);
        
        // 分拣单明细操作
        Task<List<PkmconModel>> GetPkmconListAsync(string pkmId);
        Task<PkmconModel> GetPkmconByIdAsync(string id);
        Task<bool> SavePkmconAsync(PkmconModel pkmcon);
        Task<bool> UpdatePkmconQuantityAsync(string id, decimal actualQuantity);
        
        // 分拣单SKU明细操作
        Task<List<PkmskuModel>> GetPkmskuListAsync(string pkmId);
        Task<PkmskuModel> GetPkmskuByIdAsync(string id);
        Task<bool> SavePkmskuAsync(PkmskuModel pkmsku);
        Task<bool> UpdatePkmskuQuantityAsync(string id, decimal actualQuantity);
        
        // 分拣扫描相关
        Task<ScanResultModel> ProcessPkmScanAsync(string ord, string skuCod, string loc, decimal qty);
        Task<bool> CompletePkmScanAsync(string pkmId);
        
        // 分拣任务分配
        Task<List<PkmModel>> GetPendingPkmTasksAsync(string userId);
        Task<bool> AssignPkmTaskAsync(string pkmId, string userId);
        Task<bool> StartPkmTaskAsync(string pkmId, string userId);
        Task<bool> CompletePkmTaskAsync(string pkmId, string userId);
        
        #endregion
        
        #region 出库统计
        
        // 获取出库统计信息
        Task<Dictionary<string, int>> GetOutboundStatisticsAsync();
        Task<Dictionary<string, int>> GetUserOutboundStatisticsAsync(string userId);
        Task<List<PkmModel>> GetTodayCompletedPkmAsync();
        
        #endregion
        
        #region 通用方法
        
        // 清理临时存储
        Task ClearTempStorageAsync();
        
        // 验证库存可用性
        Task<bool> ValidateInventoryAvailabilityAsync(string skuCod, string loc, decimal qty);
        
        #endregion
    }
}
