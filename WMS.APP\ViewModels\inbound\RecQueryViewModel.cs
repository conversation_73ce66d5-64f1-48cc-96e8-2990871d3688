using System.Collections.ObjectModel;
using System.ComponentModel;
using System.Runtime.CompilerServices;
using System.Windows.Input;
using WMS.MODEL;
using WMS.SERVICE;

namespace WMS.APP.ViewModels.Inbound
{
    public class RecQueryViewModel : INotifyPropertyChanged
    {
        private readonly IInboundService _inboundService;
        private string _searchKeyword = string.Empty;
        private bool _isRefreshing = false;
        private ReceiveOrderModel _selectedRecord;
        private string _currentFilter = "All";

        public ObservableCollection<ReceiveOrderModel> Records { get; } = new();

        public string SearchKeyword
        {
            get => _searchKeyword;
            set
            {
                _searchKeyword = value;
                OnPropertyChanged();
            }
        }

        public bool IsRefreshing
        {
            get => _isRefreshing;
            set
            {
                _isRefreshing = value;
                OnPropertyChanged();
            }
        }

        public ReceiveOrderModel SelectedRecord
        {
            get => _selectedRecord;
            set
            {
                _selectedRecord = value;
                OnPropertyChanged();
                OnPropertyChanged(nameof(HasSelectedRecord));
            }
        }

        public bool HasSelectedRecord => SelectedRecord != null;

        // 按钮颜色属性
        public Color AllButtonColor => _currentFilter == "All" ? Colors.Blue : Colors.LightGray;
        public Color PendingButtonColor => _currentFilter == "Pending" ? Colors.Orange : Colors.LightGray;
        public Color CompletedButtonColor => _currentFilter == "Completed" ? Colors.Green : Colors.LightGray;

        // 命令
        public ICommand SearchCommand { get; }
        public ICommand RefreshCommand { get; }
        public ICommand FilterCommand { get; }
        public ICommand CreateCommand { get; }
        public ICommand ViewDetailsCommand { get; }
        public ICommand SelectionChangedCommand { get; }

        public RecQueryViewModel(IInboundService inboundService)
        {
            _inboundService = inboundService;

            SearchCommand = new Command(async () => await SearchRecords());
            RefreshCommand = new Command(async () => await RefreshRecords());
            FilterCommand = new Command<string>(async (filter) => await ApplyFilter(filter));
            CreateCommand = new Command(async () => await CreateNewRecord());
            ViewDetailsCommand = new Command(async () => await ViewRecordDetails());
            SelectionChangedCommand = new Command<ReceiveOrderModel>((record) => SelectedRecord = record);

            // 初始加载数据
            _ = Task.Run(async () => await LoadRecords());
        }

        private async Task LoadRecords()
        {
            try
            {
                IsRefreshing = true;
                var records = await _inboundService.GetRecordListAsync(_searchKeyword);
                
                MainThread.BeginInvokeOnMainThread(() =>
                {
                    Records.Clear();
                    foreach (var record in records)
                    {
                        Records.Add(record);
                    }
                });
            }
            catch (Exception ex)
            {
                await ShowError($"加载数据失败: {ex.Message}");
            }
            finally
            {
                IsRefreshing = false;
            }
        }

        private async Task SearchRecords()
        {
            await LoadRecords();
        }

        private async Task RefreshRecords()
        {
            await LoadRecords();
        }

        private async Task ApplyFilter(string filter)
        {
            _currentFilter = filter;
            
            // 更新按钮颜色
            OnPropertyChanged(nameof(AllButtonColor));
            OnPropertyChanged(nameof(PendingButtonColor));
            OnPropertyChanged(nameof(CompletedButtonColor));

            try
            {
                IsRefreshing = true;
                var allRecords = await _inboundService.GetRecordListAsync(_searchKeyword);
                
                var filteredRecords = filter switch
                {
                    "Pending" => allRecords.Where(r => r.Status == "待收货" || r.Status == "新建").ToList(),
                    "Completed" => allRecords.Where(r => r.Status == "已完成").ToList(),
                    _ => allRecords
                };

                MainThread.BeginInvokeOnMainThread(() =>
                {
                    Records.Clear();
                    foreach (var record in filteredRecords)
                    {
                        Records.Add(record);
                    }
                });
            }
            catch (Exception ex)
            {
                await ShowError($"筛选数据失败: {ex.Message}");
            }
            finally
            {
                IsRefreshing = false;
            }
        }

        private async Task CreateNewRecord()
        {
            try
            {
                // 导航到新建收货单页面
                await Shell.Current.GoToAsync("//RecCreatePage");
            }
            catch (Exception ex)
            {
                await ShowError($"导航失败: {ex.Message}");
            }
        }

        private async Task ViewRecordDetails()
        {
            if (SelectedRecord == null)
            {
                await ShowError("请先选择一个收货单");
                return;
            }

            try
            {
                // 导航到收货单详情页面
                await Shell.Current.GoToAsync($"//RecDetailsPage?recordId={SelectedRecord.Id}");
            }
            catch (Exception ex)
            {
                await ShowError($"导航失败: {ex.Message}");
            }
        }

        private async Task ShowError(string message)
        {
            await Application.Current.MainPage.DisplayAlert("错误", message, "确定");
        }

        public event PropertyChangedEventHandler PropertyChanged;

        protected virtual void OnPropertyChanged([CallerMemberName] string propertyName = null)
        {
            PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
        }
    }
}
