using CommunityToolkit.Mvvm.ComponentModel;
using CommunityToolkit.Mvvm.Input;
using System.Collections.ObjectModel;
using System.Diagnostics;
using WMS.APP.Common;
using WMS.APP.Views.inbound;
using WMS.MODEL;
using WMS.SERVICE;

namespace WMS.APP.ViewModels.inbound
{
    public partial class ReceiveOrderViewModel : ObservableObject
    {
        private readonly INavigationService _navigationService;
        private readonly IInboundService _dataService;

        [ObservableProperty]
        private string _searchKeyword;

        [ObservableProperty]
        private bool _isLoadingMore;

        [ObservableProperty]
        private bool _isNoMoreData;

        [ObservableProperty]
        private bool _isRefreshing;

        [ObservableProperty]
        private int _currentPage = 1;

        private const int PageSize = 5;
        private bool _hasMoreItems = true;
        private bool _isLoading = false; // 防止重复加载

        // 使用属性而不是字段来存储集合
        private ObservableCollection<ReceiveOrderModel> _orders = new ObservableCollection<ReceiveOrderModel>();
        public ObservableCollection<ReceiveOrderModel> Orders
        {
            get => _orders;
            set => SetProperty(ref _orders, value);
        }

        public ReceiveOrderViewModel(INavigationService navigationService, IInboundService dataService)
        {
            _navigationService = navigationService;
            _dataService = dataService;

            // 初始加载数据
            //LoadOrdersAsync();
        }

        [RelayCommand]
        private async Task SearchOrder()
        {
            try
            {
                // 重置分页状态
                CurrentPage = 1;
                _hasMoreItems = true;
                IsNoMoreData = false;
                IsLoadingMore = false; // 确保加载状态被重置
                _isLoading = false;    // 确保内部加载标志被重置
                Orders.Clear();

                // 设置加载状态
                var isLoading = true;

                // 获取第一页数据
                var result = await _dataService.GetReceiveOrdersPagedAsync(SearchKeyword, CurrentPage, PageSize);

                // 如果没有数据，标记结束
                if (result == null || result.Count == 0)
                {
                    _hasMoreItems = false;
                    IsNoMoreData = false; // 没有数据，不显示"没有更多数据"
                    return;
                }

                // 添加到集合
                foreach (var order in result)
                {
                    Orders.Add(order);
                }

                // 如果返回的数据少于页大小，说明没有更多数据了
                if (result.Count < PageSize)
                {
                    _hasMoreItems = false;
                    IsNoMoreData = true;
                }
                else
                {
                    _hasMoreItems = true;
                    IsNoMoreData = false;
                }

                // 确保加载完成后重置状态
                isLoading = false;
                IsLoadingMore = false;
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"搜索订单错误: {ex.Message}");
                await Shell.Current.DisplayAlert("错误", $"搜索订单失败: {ex.Message}", "确定");
            }
            finally
            {
                // 确保加载状态被重置
                IsLoadingMore = false;
                _isLoading = false;
            }
        }

        [RelayCommand]
        private async Task Refresh()
        {
            try
            {
                // 重置分页状态
                //CurrentPage = 1;
                //_hasMoreItems = true;
                //IsNoMoreData = false;
                //Orders.Clear();

                // 获取第一页数据
                //var result = await _dataService.GetReceiveOrdersPagedAsync(SearchKeyword, CurrentPage, PageSize);

                // 如果没有数据，标记结束
                /*if (result == null || result.Count == 0)
                {
                    _hasMoreItems = false;
                    IsNoMoreData = false; // 没有数据，不显示"没有更多数据"
                    return;
                }

                // 添加到集合
                foreach (var order in result)
                {
                    Orders.Add(order);
                }

                // 如果返回的数据少于页大小，说明没有更多数据了
                if (result.Count < PageSize)
                {
                    _hasMoreItems = false;
                    IsNoMoreData = true;
                }
                else
                {
                    _hasMoreItems = true;
                    IsNoMoreData = false;
                }*/
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"刷新订单错误: {ex.Message}");
                await Shell.Current.DisplayAlert("错误", $"刷新订单失败: {ex.Message}", "确定");
            }
            finally
            {
                IsRefreshing = false;
                IsLoadingMore = false;
                _isLoading = false;
            }
        }

        // 滑动到底部时自动加载更多
        public async Task LoadMoreItems()
        {
            // 如果正在加载、已经在加载更多、没有更多数据，或者当前页小于等于1，则不执行加载
            if (_isLoading || IsLoadingMore || !_hasMoreItems || CurrentPage < 1)
            {
                // 确保加载状态被重置
                IsLoadingMore = false;
                _isLoading = false;
                return;
            }

            try
            {
                _isLoading = true;
                IsLoadingMore = true;

                // 页码递增
                CurrentPage++;

                // 安全检查：确保页码有效
                if (CurrentPage < 1)
                {
                    CurrentPage = 1;
                    IsLoadingMore = false;
                    _isLoading = false;
                    return;
                }

                // 获取当前页的数据
                var result = await _dataService.GetReceiveOrdersPagedAsync(SearchKeyword, CurrentPage, PageSize);

                // 如果没有更多数据，标记结束
                if (result == null || result.Count == 0)
                {
                    _hasMoreItems = false;
                    IsNoMoreData = Orders.Any();
                    return;
                }

                // 添加到现有集合
                foreach (var order in result)
                {
                    Orders.Add(order);
                }

                // 如果返回的数据少于页大小，说明没有更多数据了
                if (result.Count < PageSize)
                {
                    _hasMoreItems = false;
                    IsNoMoreData = true;
                }
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"加载订单错误: {ex.Message}");
                await Shell.Current.DisplayAlert("错误", $"加载订单失败: {ex.Message}", "确定");
            }
            finally
            {
                // 确保加载状态被重置
                IsLoadingMore = false;
                _isLoading = false;

                // 主线程更新UI状态
                MainThread.BeginInvokeOnMainThread(() =>
                {
                    IsLoadingMore = false;
                });
            }
        }

        [RelayCommand]
        private async Task CreateOrder()
        {
            try
            {
                // 打印调试信息
                Debug.WriteLine($"尝试导航到: {nameof(ReceiveOrderEditPage)}");

                // 检查页面类型是否正确解析
                var pageType = typeof(ReceiveOrderEditPage);
                Debug.WriteLine($"页面类型: {pageType.FullName}");

                // 使用特殊ID "new" 表示新建订单
                await _navigationService.NavigateToAsync($"{nameof(ReceiveOrderEditPage)}?orderId=new");
            }
            catch (Exception ex)
            {
                // 详细记录异常信息
                Debug.WriteLine($"导航错误: {ex.Message}");
                Debug.WriteLine($"异常类型: {ex.GetType().Name}");
                Debug.WriteLine($"堆栈跟踪: {ex.StackTrace}");

                // 5显示错误消息
                await Shell.Current.DisplayAlert("错误", $"创建订单失败: {ex.Message}", "确定");
            }
        }

        [RelayCommand]
        private async Task EditOrder(ReceiveOrderModel order)
        {
            if (order != null)
            {
                // 传递订单ID而不是整个对象
                await _navigationService.NavigateToAsync($"{nameof(ReceiveOrderEditPage)}?orderId={order.Id}");
            }
        }

        [RelayCommand]
        private async Task DeleteOrder(ReceiveOrderModel order)
        {
            if (order == null) return;

            bool confirm = await Shell.Current.DisplayAlert("确认", "确定要删除此订单吗?", "确定", "取消");
            if (confirm)
            {
                // 调用服务删除订单
                await _dataService.DeleteReceiveOrderAsync(order.Id);
                // 从集合中移除订单
                Orders.Remove(order);
                // 刷新列表
                await LoadOrdersAsync();
            }
        }

        [RelayCommand]
        private async Task ViewOrderDetails(ReceiveOrderModel order)
        {
            if (order != null)
            {
                // 只传递 ID，而不是整个对象
                await _navigationService.NavigateToAsync($"{nameof(ReceiveOrderDetailsPage)}?orderId={order.Id}");
            }
        }

        private async Task LoadOrdersAsync(bool isLoadingMore = false)
        {
            try
            {
                if (isLoadingMore)
                {
                    IsLoadingMore = true;
                }

                // 获取当前页的数据
                var result = await _dataService.GetReceiveOrdersPagedAsync(SearchKeyword, CurrentPage, PageSize);

                // 如果没有更多数据，标记结束
                if (result.Count == 0)
                {
                    _hasMoreItems = false;
                    IsNoMoreData = Orders.Any();
                    return;
                }

                // 添加到现有集合
                foreach (var order in result)
                {
                    Orders.Add(order);
                }

                // 如果返回的数据少于页大小，说明没有更多数据了
                if (result.Count < PageSize)
                {
                    _hasMoreItems = false;
                    IsNoMoreData = true;
                }
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"加载订单错误: {ex.Message}");
                await Shell.Current.DisplayAlert("错误", $"加载订单失败: {ex.Message}", "确定");
            }
            finally
            {
                // 确保加载状态被重置
                IsLoadingMore = false;
            }
        }

        private string GenerateOrderNumber()
        {
            return $"RCV{DateTime.Now:yyyyMMddHHmmss}";
        }
    }
}













