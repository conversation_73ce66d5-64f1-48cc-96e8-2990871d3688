<?xml version="1.0" encoding="utf-8" ?>
<ContentPage xmlns="http://schemas.microsoft.com/dotnet/2021/maui"
             xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
             x:Class="WMS.APP.Views.system.ProfilePage"
             Title="用户信息">
    <VerticalStackLayout>
        <Label Text="个人信息" FontSize="30" HorizontalOptions="Center"/>

        <!-- 信息展示区域 -->
        <VerticalStackLayout Spacing="25"  Padding="20">
            <VerticalStackLayout.Resources>
                <Style TargetType="Label">
                    <Setter Property="FontSize" Value="16"/>
                </Style>
            </VerticalStackLayout.Resources>
            <!-- 每一项使用 Grid 实现左右对齐 -->
            <Grid ColumnDefinitions="*,Auto">
                <Label Text="用户名：" VerticalOptions="Center"/>
                <Label Text="{Binding Username}" HorizontalOptions="Start" Grid.Column="1"/>
            </Grid>

            <Grid ColumnDefinitions="*,Auto">
                <Label Text="真实姓名：" VerticalOptions="Center"/>
                <Label Text="{Binding RealName}" HorizontalOptions="Start" Grid.Column="1"/>
            </Grid>

            <Grid ColumnDefinitions="*,Auto">
                <Label Text="部门：" VerticalOptions="Center"/>
                <Label Text="{Binding Department}" HorizontalOptions="Start" Grid.Column="1"/>
            </Grid>

            <Grid ColumnDefinitions="*,Auto">
                <Label Text="版本号：" VerticalOptions="Center"/>
                <Label Text="{Binding Version}" HorizontalOptions="End" Grid.Column="1"/>
            </Grid>
            <Button Text="退出登录" Grid.Row="1"
                Command="{Binding LogoutCommand}"
                BackgroundColor="Red"
                TextColor="White"
                HorizontalOptions="Center"   
                WidthRequest="300"
                Margin="0,40,0,0"/>
        </VerticalStackLayout>

    </VerticalStackLayout>
</ContentPage>