using System.Collections.ObjectModel;

namespace WMS.MODEL
{
    /// <summary>
    /// 收货单主表 - REC
    /// </summary>
    public class RecordModel
    {
        public string Id { get; set; } = Guid.NewGuid().ToString();

        /// <summary>
        /// 客户订单号 - CON
        /// </summary>
        public string CON { get; set; }

        /// <summary>
        /// 系统订单号 - ORD
        /// </summary>
        public string ORD { get; set; }

        public string Supplier { get; set; }
        public DateTime CreateDate { get; set; } = DateTime.Now;
        public DateTime ExpectedDate { get; set; } = DateTime.Now.AddDays(7);
        public string Status { get; set; } = "新建";
        public string Remarks { get; set; }

        // 汇总信息
        public int TotalItems => Details?.Count ?? 0;
        public decimal TotalQuantity => Details?.Sum(d => d.QTY) ?? 0;
        public int CompletedItems => Details?.Count(d => d.ActualQuantity >= d.QTY) ?? 0;

        public ObservableCollection<RecconModel> Details { get; set; } = new();
    }
}