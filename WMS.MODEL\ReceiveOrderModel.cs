using System.Collections.ObjectModel;

namespace WMS.MODEL
{
    public class ReceiveOrderModel
    {
        public string Id { get; set; } = Guid.NewGuid().ToString();
        public string OrderNumber { get; set; }
        public string Supplier { get; set; }
        public DateTime CreateDate { get; set; } = DateTime.Now;
        public DateTime ExpectedDate { get; set; } = DateTime.Now.AddDays(7);
        public string Status { get; set; } = "新建";
        public string Remarks { get; set; }

        // 汇总信息
        public int TotalItems => Details?.Count ?? 0;
        public decimal TotalQuantity => Details?.Sum(d => d.PlannedQuantity) ?? 0;
        public int CompletedItems => Details?.Count(d => d.ActualQuantity >= d.PlannedQuantity) ?? 0;

        public ObservableCollection<ReceiveOrderDetailModel> Details { get; set; } = new();
    }
}