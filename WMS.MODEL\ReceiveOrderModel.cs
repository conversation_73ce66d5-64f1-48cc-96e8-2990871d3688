using System.Collections.ObjectModel;

namespace WMS.MODEL
{
    /// <summary>
    /// 收货单主表 - REC
    /// </summary>
    public class ReceiveOrderModel
    {
        public string Id { get; set; } = Guid.NewGuid().ToString();

        /// <summary>
        /// 客户订单号 - CON
        /// </summary>
        public string CON { get; set; }

        /// <summary>
        /// 系统订单号 - ORD
        /// </summary>
        public string ORD { get; set; }

        public string Supplier { get; set; }
        public DateTime CreateDate { get; set; } = DateTime.Now;
        public DateTime ExpectedDate { get; set; } = DateTime.Now.AddDays(7);
        public string Status { get; set; } = "新建";
        public string Remarks { get; set; }

        // 汇总信息
        public int TotalItems => Details?.Count ?? 0;
        public decimal TotalQuantity => Details?.Sum(d => d.QTY) ?? 0;
        public int CompletedItems => Details?.Count(d => d.ActualQuantity >= d.QTY) ?? 0;

        public ObservableCollection<ReceiveOrderDetailModel> Details { get; set; } = new();
    }

    /// <summary>
    /// 收货记录模型 - 用于扫描操作
    /// </summary>
    public class RecordModel
    {
        /// <summary>
        /// 记录ID
        /// </summary>
        public int Id { get; set; }

        /// <summary>
        /// 系统订单号
        /// </summary>
        public string ORD { get; set; } = string.Empty;

        /// <summary>
        /// 客户订单号
        /// </summary>
        public string CON { get; set; } = string.Empty;

        /// <summary>
        /// 状态
        /// </summary>
        public string Status { get; set; } = "待收货";

        /// <summary>
        /// 创建时间
        /// </summary>
        public DateTime CreateTime { get; set; } = DateTime.Now;

        /// <summary>
        /// 更新时间
        /// </summary>
        public DateTime UpdateTime { get; set; } = DateTime.Now;

        /// <summary>
        /// 操作员
        /// </summary>
        public string Operator { get; set; } = string.Empty;

        /// <summary>
        /// 备注
        /// </summary>
        public string Remark { get; set; } = string.Empty;
    }

    /// <summary>
    /// 收货记录明细模型 - 类型别名，指向现有的ReceiveOrderDetailModel
    /// </summary>
    public class RecconModel : ReceiveOrderDetailModel
    {
        // 继承ReceiveOrderDetailModel的所有属性
        // 这样可以保持兼容性
    }
}