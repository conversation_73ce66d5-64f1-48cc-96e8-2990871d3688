﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using WMS.MODEL;

namespace WMS.SERVICE
{
    /// <summary>
    /// 库存服务接口
    /// </summary>
    public interface IInventoryService
    {
        #region 库存查询 (INV)

        // 库存主表操作
        Task<List<InvModel>> GetInvListAsync(string keyword = null, int page = 1, int pageSize = 20);
        Task<InvModel> GetInvByIdAsync(string id);
        Task<List<InvModel>> GetInvBySkuAsync(string skuCod);
        Task<List<InvModel>> GetInvByLocationAsync(string loc);
        Task<InvModel> GetInvBySkuAndLocationAsync(string skuCod, string loc, string lot = null);
        Task<bool> SaveInvAsync(InvModel inv);
        Task<bool> DeleteInvAsync(string id);

        // 库存数量操作
        Task<bool> UpdateInvQuantityAsync(string id, decimal qty);
        Task<bool> AddInvQuantityAsync(string skuCod, string loc, decimal qty, string lot = null);
        Task<bool> ReduceInvQuantityAsync(string skuCod, string loc, decimal qty, string lot = null);

        // 库存查询
        Task<List<InvModel>> SearchInvAsync(string skuCod = null, string loc = null, string lot = null);
        Task<decimal> GetAvailableQuantityAsync(string skuCod, string loc, string lot = null);
        Task<bool> CheckInventoryAvailabilityAsync(string skuCod, string loc, decimal requiredQty, string lot = null);

        #endregion

        #region 移库操作 (INVTRANSFER)

        // 移库操作
        Task<List<InvTransferModel>> GetInvTransferListAsync(string keyword = null, int page = 1, int pageSize = 20);
        Task<InvTransferModel> GetInvTransferByIdAsync(string id);
        Task<bool> SaveInvTransferAsync(InvTransferModel transfer);
        Task<bool> DeleteInvTransferAsync(string id);

        // 移库扫描相关
        Task<ScanResultModel> ProcessInvTransferScanAsync(string skuCod, string sourceLoc, string targetLoc, decimal qty, string lot = null);
        Task<bool> CompleteInvTransferAsync(string transferId);
        Task<bool> ExecuteInvTransferAsync(InvTransferModel transfer);

        #endregion

        #region 库存统计

        // 获取库存统计信息
        Task<Dictionary<string, object>> GetInventoryStatisticsAsync();
        Task<Dictionary<string, decimal>> GetInventoryByLocationStatisticsAsync();
        Task<Dictionary<string, decimal>> GetInventoryBySkuStatisticsAsync();

        #endregion

        #region 通用方法

        // 清理临时存储
        Task ClearTempStorageAsync();

        // 库存同步
        Task<bool> SyncInventoryAsync();

        #endregion
    }
}
