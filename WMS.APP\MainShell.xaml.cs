﻿namespace WMS.APP
{
    public partial class MainShell : Shell
    {
        public MainShell()
        {
            InitializeComponent();
        }

        // UnComment the below method to handle Shell Menu item click event
        // And ensure appropriate page definitions are available for it work as expected
        /*
        private async void OnMenuItemClicked(object sender, EventArgs e)
        {
            await Current.GoToAsync("//login");
        }
        */
        // MainPage.xaml.cs
        protected override bool OnBackButtonPressed()
        {
            if (Application.Current is App app)
            {
                app.ConfirmExit().ContinueWith(t =>
                {
                    if (t.Result)
                    {
                        Application.Current.Quit();
                    }
                }, TaskScheduler.FromCurrentSynchronizationContext());
            }
            return true; // 拦截返回键
        }
    }
}
