﻿using CommunityToolkit.Mvvm.ComponentModel;
using CommunityToolkit.Mvvm.Input;
using System.Diagnostics;
using WMS.SERVICE;

namespace WMS.APP.ViewModels.system
{
    public partial class ProfileViewModel : ObservableObject
    {
        private readonly IUserService _userService;

        public ProfileViewModel(IUserService userService)
        {
            //Username = Preferences.Get("testonline", "admin");
            //RealName = Preferences.Get("张三", "admin");
            //Department = Preferences.Get("市场部", "admin");
            Version = AppInfo.Current.VersionString;
            _userService = userService;
            LoadUserInfoAsync();
        }

        [ObservableProperty]
        private string username;

        [ObservableProperty]
        private string realName;

        [ObservableProperty]
        private string department;

        [ObservableProperty]
        private string version;

        [RelayCommand]
        private async Task Logout()
        {
            if (Application.Current is App app)
            {
                bool shouldExit = await app.ConfirmExit(); // Await the task to fix CS4014
                if (shouldExit)
                {
                    await app.GoToLoginPage(); // Await the task to fix CS4014
                }
            }
        }

        //异步加载用户信息
        private Task LoadUserInfoAsync()
        {
            return Task.Run(async () =>
            {
                try
                {
                    var userInfo = await _userService.GetUserInfoAsync(Username, "password"); // 假设密码是"password"
                    if (!string.IsNullOrEmpty(userInfo))
                    {
                        // 解析并设置用户信息
                        // 这里假设userInfo是一个JSON字符串，你可以根据实际情况进行解析
                        // 例如：var user = JsonSerializer.Deserialize<User>(userInfo);
                        // 设置属性
                        RealName = "张三"; // 示例值
                        Department = "市场部"; // 示例值
                    }
                }
                catch (Exception ex)
                {
                    // 处理异常
                    Debug.WriteLine($"加载用户信息失败: {ex.Message}");
                }
            });
        }

    }
}
