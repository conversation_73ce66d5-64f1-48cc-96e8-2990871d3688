using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Text.Json;
using System.Threading.Tasks;
using WMS.MODEL;
using WMS.SERVICE.Utils;

namespace WMS.SERVICE
{
    /// <summary>
    /// 出库服务实现
    /// </summary>
    public class OutboundService : IOutboundService
    {
        private const string PKM_KEY = "pkms";
        private const string PKMCON_KEY = "pkmcons";
        private const string PKMSKU_KEY = "pkmskus";
        
        private readonly ApiHelperr _apiHelper;

        public OutboundService()
        {
            _apiHelper = new ApiHelperr();
        }

        #region 分拣管理 (PKM)

        public async Task<List<PkmModel>> GetPkmListAsync(string keyword = null, int page = 1, int pageSize = 20)
        {
            try
            {
                var apiUrl = $"/api/outbound/pkms?keyword={keyword}&page={page}&pageSize={pageSize}";
                var result = await _apiHelper.GetAsync<List<PkmModel>>(apiUrl);
                if (result != null)
                    return result;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"API调用失败: {ex.Message}");
            }

            var pkms = await GetStoredDataAsync<PkmModel>(PKM_KEY);
            
            if (!string.IsNullOrWhiteSpace(keyword))
            {
                keyword = keyword.ToLower();
                pkms = pkms.Where(p => 
                    p.CON?.ToLower().Contains(keyword) == true ||
                    p.ORD?.ToLower().Contains(keyword) == true
                ).ToList();
            }

            return pkms
                .OrderByDescending(p => p.CreateDate)
                .Skip((page - 1) * pageSize)
                .Take(pageSize)
                .ToList();
        }

        public async Task<PkmModel> GetPkmByIdAsync(string id)
        {
            try
            {
                var apiUrl = $"/api/outbound/pkms/{id}";
                var result = await _apiHelper.GetAsync<PkmModel>(apiUrl);
                if (result != null)
                    return result;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"API调用失败: {ex.Message}");
            }

            var pkms = await GetStoredDataAsync<PkmModel>(PKM_KEY);
            return pkms.FirstOrDefault(p => p.Id == id);
        }

        public async Task<PkmModel> GetPkmByOrderAsync(string ord, string con = null)
        {
            try
            {
                var apiUrl = $"/api/outbound/pkms/byorder?ord={ord}&con={con}";
                var result = await _apiHelper.GetAsync<PkmModel>(apiUrl);
                if (result != null)
                    return result;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"API调用失败: {ex.Message}");
            }

            var pkms = await GetStoredDataAsync<PkmModel>(PKM_KEY);
            return pkms.FirstOrDefault(p => p.ORD == ord && (con == null || p.CON == con));
        }

        public async Task<bool> SavePkmAsync(PkmModel pkm)
        {
            try
            {
                var apiUrl = "/api/outbound/pkms";
                var result = await _apiHelper.PostAsync<PkmModel, bool>(apiUrl, pkm);
                if (result)
                {
                    await SaveToLocalStorageAsync(PKM_KEY, pkm);
                    return true;
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"API调用失败: {ex.Message}");
            }

            await SaveToLocalStorageAsync(PKM_KEY, pkm);
            return true;
        }

        public async Task<bool> DeletePkmAsync(string id)
        {
            try
            {
                var apiUrl = $"/api/outbound/pkms/{id}";
                var result = await _apiHelper.DeleteAsync(apiUrl);
                if (result)
                {
                    await RemoveFromLocalStorageAsync<PkmModel>(PKM_KEY, id);
                    return true;
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"API调用失败: {ex.Message}");
            }

            await RemoveFromLocalStorageAsync<PkmModel>(PKM_KEY, id);
            return true;
        }

        public async Task<List<PkmconModel>> GetPkmconListAsync(string pkmId)
        {
            try
            {
                var apiUrl = $"/api/outbound/pkmcons?pkmId={pkmId}";
                var result = await _apiHelper.GetAsync<List<PkmconModel>>(apiUrl);
                if (result != null)
                    return result;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"API调用失败: {ex.Message}");
            }

            var pkmcons = await GetStoredDataAsync<PkmconModel>(PKMCON_KEY);
            return pkmcons.Where(p => p.PkmId == pkmId).ToList();
        }

        public async Task<PkmconModel> GetPkmconByIdAsync(string id)
        {
            try
            {
                var apiUrl = $"/api/outbound/pkmcons/{id}";
                var result = await _apiHelper.GetAsync<PkmconModel>(apiUrl);
                if (result != null)
                    return result;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"API调用失败: {ex.Message}");
            }

            var pkmcons = await GetStoredDataAsync<PkmconModel>(PKMCON_KEY);
            return pkmcons.FirstOrDefault(p => p.Id == id);
        }

        public async Task<bool> SavePkmconAsync(PkmconModel pkmcon)
        {
            try
            {
                var apiUrl = "/api/outbound/pkmcons";
                var result = await _apiHelper.PostAsync<PkmconModel, bool>(apiUrl, pkmcon);
                if (result)
                {
                    await SaveToLocalStorageAsync(PKMCON_KEY, pkmcon);
                    return true;
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"API调用失败: {ex.Message}");
            }

            await SaveToLocalStorageAsync(PKMCON_KEY, pkmcon);
            return true;
        }

        public async Task<bool> UpdatePkmconQuantityAsync(string id, decimal actualQuantity)
        {
            try
            {
                var apiUrl = $"/api/outbound/pkmcons/{id}/quantity";
                var result = await _apiHelper.PutAsync<decimal, bool>(apiUrl, actualQuantity);
                if (result)
                {
                    var pkmcons = await GetStoredDataAsync<PkmconModel>(PKMCON_KEY);
                    var pkmcon = pkmcons.FirstOrDefault(p => p.Id == id);
                    if (pkmcon != null)
                    {
                        pkmcon.ActualQuantity = actualQuantity;
                        await SaveAllToLocalStorageAsync(PKMCON_KEY, pkmcons);
                    }
                    return true;
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"API调用失败: {ex.Message}");
            }

            var localPkmcons = await GetStoredDataAsync<PkmconModel>(PKMCON_KEY);
            var localPkmcon = localPkmcons.FirstOrDefault(p => p.Id == id);
            if (localPkmcon != null)
            {
                localPkmcon.ActualQuantity = actualQuantity;
                await SaveAllToLocalStorageAsync(PKMCON_KEY, localPkmcons);
                return true;
            }

            return false;
        }

        public async Task<List<PkmskuModel>> GetPkmskuListAsync(string pkmId)
        {
            try
            {
                var apiUrl = $"/api/outbound/pkmskus?pkmId={pkmId}";
                var result = await _apiHelper.GetAsync<List<PkmskuModel>>(apiUrl);
                if (result != null)
                    return result;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"API调用失败: {ex.Message}");
            }

            var pkmskus = await GetStoredDataAsync<PkmskuModel>(PKMSKU_KEY);
            return pkmskus.Where(p => p.PkmId == pkmId).ToList();
        }

        public async Task<PkmskuModel> GetPkmskuByIdAsync(string id)
        {
            try
            {
                var apiUrl = $"/api/outbound/pkmskus/{id}";
                var result = await _apiHelper.GetAsync<PkmskuModel>(apiUrl);
                if (result != null)
                    return result;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"API调用失败: {ex.Message}");
            }

            var pkmskus = await GetStoredDataAsync<PkmskuModel>(PKMSKU_KEY);
            return pkmskus.FirstOrDefault(p => p.Id == id);
        }

        public async Task<bool> SavePkmskuAsync(PkmskuModel pkmsku)
        {
            try
            {
                var apiUrl = "/api/outbound/pkmskus";
                var result = await _apiHelper.PostAsync<PkmskuModel, bool>(apiUrl, pkmsku);
                if (result)
                {
                    await SaveToLocalStorageAsync(PKMSKU_KEY, pkmsku);
                    return true;
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"API调用失败: {ex.Message}");
            }

            await SaveToLocalStorageAsync(PKMSKU_KEY, pkmsku);
            return true;
        }

        public async Task<bool> UpdatePkmskuQuantityAsync(string id, decimal actualQuantity)
        {
            try
            {
                var apiUrl = $"/api/outbound/pkmskus/{id}/quantity";
                var result = await _apiHelper.PutAsync<decimal, bool>(apiUrl, actualQuantity);
                if (result)
                {
                    var pkmskus = await GetStoredDataAsync<PkmskuModel>(PKMSKU_KEY);
                    var pkmsku = pkmskus.FirstOrDefault(p => p.Id == id);
                    if (pkmsku != null)
                    {
                        pkmsku.ActualQuantity = actualQuantity;
                        await SaveAllToLocalStorageAsync(PKMSKU_KEY, pkmskus);
                    }
                    return true;
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"API调用失败: {ex.Message}");
            }

            var localPkmskus = await GetStoredDataAsync<PkmskuModel>(PKMSKU_KEY);
            var localPkmsku = localPkmskus.FirstOrDefault(p => p.Id == id);
            if (localPkmsku != null)
            {
                localPkmsku.ActualQuantity = actualQuantity;
                await SaveAllToLocalStorageAsync(PKMSKU_KEY, localPkmskus);
                return true;
            }

            return false;
        }

        public async Task<ScanResultModel> ProcessPkmScanAsync(string ord, string skuCod, string loc, decimal qty)
        {
            try
            {
                var scanData = new
                {
                    ORD = ord,
                    SKUCOD = skuCod,
                    LOC = loc,
                    QTY = qty
                };

                var apiUrl = "/api/outbound/scan/pkm";
                var result = await _apiHelper.PostAsync<object, ScanResultModel>(apiUrl, scanData);
                if (result != null)
                    return result;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"API调用失败: {ex.Message}");
            }

            return new ScanResultModel
            {
                IsSuccess = true,
                Message = "分拣扫描成功（本地处理）",
                ScannedValue = $"{ord}-{skuCod}-{loc}-{qty}",
                NextFieldId = "Quantity"
            };
        }

        public async Task<bool> CompletePkmScanAsync(string pkmId)
        {
            try
            {
                var apiUrl = $"/api/outbound/scan/pkm/{pkmId}/complete";
                var result = await _apiHelper.PostAsync<object, bool>(apiUrl, null);
                return result;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"API调用失败: {ex.Message}");
                return true;
            }
        }

        #endregion

        #region 分拣任务管理

        public async Task<List<PkmModel>> GetPendingPkmTasksAsync(string userId)
        {
            try
            {
                var apiUrl = $"/api/outbound/tasks/pending?userId={userId}";
                var result = await _apiHelper.GetAsync<List<PkmModel>>(apiUrl);
                if (result != null)
                    return result;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"API调用失败: {ex.Message}");
            }

            var pkms = await GetStoredDataAsync<PkmModel>(PKM_KEY);
            return pkms.Where(p => p.Status == "待分拣").ToList();
        }

        public async Task<bool> AssignPkmTaskAsync(string pkmId, string userId)
        {
            try
            {
                var apiUrl = $"/api/outbound/tasks/{pkmId}/assign";
                var result = await _apiHelper.PostAsync<string, bool>(apiUrl, userId);
                return result;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"API调用失败: {ex.Message}");
                return true;
            }
        }

        public async Task<bool> StartPkmTaskAsync(string pkmId, string userId)
        {
            try
            {
                var apiUrl = $"/api/outbound/tasks/{pkmId}/start";
                var result = await _apiHelper.PostAsync<string, bool>(apiUrl, userId);
                if (result)
                {
                    var pkms = await GetStoredDataAsync<PkmModel>(PKM_KEY);
                    var pkm = pkms.FirstOrDefault(p => p.Id == pkmId);
                    if (pkm != null)
                    {
                        pkm.Status = "分拣中";
                        await SaveAllToLocalStorageAsync(PKM_KEY, pkms);
                    }
                }
                return result;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"API调用失败: {ex.Message}");
                return true;
            }
        }

        public async Task<bool> CompletePkmTaskAsync(string pkmId, string userId)
        {
            try
            {
                var apiUrl = $"/api/outbound/tasks/{pkmId}/complete";
                var result = await _apiHelper.PostAsync<string, bool>(apiUrl, userId);
                if (result)
                {
                    var pkms = await GetStoredDataAsync<PkmModel>(PKM_KEY);
                    var pkm = pkms.FirstOrDefault(p => p.Id == pkmId);
                    if (pkm != null)
                    {
                        pkm.Status = "已完成";
                        await SaveAllToLocalStorageAsync(PKM_KEY, pkms);
                    }
                }
                return result;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"API调用失败: {ex.Message}");
                return true;
            }
        }

        #endregion

        #region 出库统计

        public async Task<Dictionary<string, int>> GetOutboundStatisticsAsync()
        {
            try
            {
                var apiUrl = "/api/outbound/statistics";
                var result = await _apiHelper.GetAsync<Dictionary<string, int>>(apiUrl);
                if (result != null)
                    return result;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"API调用失败: {ex.Message}");
            }

            var pkms = await GetStoredDataAsync<PkmModel>(PKM_KEY);
            return new Dictionary<string, int>
            {
                ["TotalPkms"] = pkms.Count,
                ["PendingPkms"] = pkms.Count(p => p.Status == "待分拣"),
                ["InProgressPkms"] = pkms.Count(p => p.Status == "分拣中"),
                ["CompletedPkms"] = pkms.Count(p => p.Status == "已完成")
            };
        }

        public async Task<Dictionary<string, int>> GetUserOutboundStatisticsAsync(string userId)
        {
            try
            {
                var apiUrl = $"/api/outbound/statistics/user/{userId}";
                var result = await _apiHelper.GetAsync<Dictionary<string, int>>(apiUrl);
                if (result != null)
                    return result;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"API调用失败: {ex.Message}");
            }

            return new Dictionary<string, int>
            {
                ["TodayCompleted"] = 0,
                ["WeekCompleted"] = 0,
                ["MonthCompleted"] = 0
            };
        }

        public async Task<List<PkmModel>> GetTodayCompletedPkmAsync()
        {
            try
            {
                var apiUrl = "/api/outbound/completed/today";
                var result = await _apiHelper.GetAsync<List<PkmModel>>(apiUrl);
                if (result != null)
                    return result;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"API调用失败: {ex.Message}");
            }

            var pkms = await GetStoredDataAsync<PkmModel>(PKM_KEY);
            var today = DateTime.Today;
            return pkms.Where(p => p.Status == "已完成" && p.CreateDate.Date == today).ToList();
        }

        #endregion

        #region 通用方法

        public async Task ClearTempStorageAsync()
        {
            try
            {
                SecureStorage.Remove(PKM_KEY);
                SecureStorage.Remove(PKMCON_KEY);
                SecureStorage.Remove(PKMSKU_KEY);
            }
            catch (Exception ex)
            {
                Console.WriteLine($"清理临时存储失败: {ex.Message}");
            }
        }

        public async Task<bool> ValidateInventoryAvailabilityAsync(string skuCod, string loc, decimal qty)
        {
            try
            {
                var apiUrl = $"/api/inventory/validate?skuCod={skuCod}&loc={loc}&qty={qty}";
                var result = await _apiHelper.GetAsync<bool>(apiUrl);
                return result;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"API调用失败: {ex.Message}");
                return true; // 本地验证默认通过
            }
        }

        #endregion

        #region 私有辅助方法

        private async Task<List<T>> GetStoredDataAsync<T>(string key) where T : class
        {
            try
            {
                string json = await SecureStorage.GetAsync(key);
                if (string.IsNullOrEmpty(json))
                    return new List<T>();

                return JsonSerializer.Deserialize<List<T>>(json) ?? new List<T>();
            }
            catch (Exception ex)
            {
                Console.WriteLine($"获取本地数据失败: {ex.Message}");
                return new List<T>();
            }
        }

        private async Task SaveToLocalStorageAsync<T>(string key, T item) where T : class
        {
            try
            {
                var items = await GetStoredDataAsync<T>(key);

                var existingIndex = -1;
                if (item is PkmModel pkm)
                    existingIndex = items.FindIndex(i => (i as PkmModel)?.Id == pkm.Id);
                else if (item is PkmconModel pkmcon)
                    existingIndex = items.FindIndex(i => (i as PkmconModel)?.Id == pkmcon.Id);
                else if (item is PkmskuModel pkmsku)
                    existingIndex = items.FindIndex(i => (i as PkmskuModel)?.Id == pkmsku.Id);

                if (existingIndex >= 0)
                    items[existingIndex] = item;
                else
                    items.Add(item);

                await SaveAllToLocalStorageAsync(key, items);
            }
            catch (Exception ex)
            {
                Console.WriteLine($"保存本地数据失败: {ex.Message}");
            }
        }

        private async Task SaveAllToLocalStorageAsync<T>(string key, List<T> items) where T : class
        {
            try
            {
                string json = JsonSerializer.Serialize(items);
                await SecureStorage.SetAsync(key, json);
            }
            catch (Exception ex)
            {
                Console.WriteLine($"保存所有本地数据失败: {ex.Message}");
            }
        }

        private async Task RemoveFromLocalStorageAsync<T>(string key, string id) where T : class
        {
            try
            {
                var items = await GetStoredDataAsync<T>(key);

                if (typeof(T) == typeof(PkmModel))
                    items.RemoveAll(i => (i as PkmModel)?.Id == id);
                else if (typeof(T) == typeof(PkmconModel))
                    items.RemoveAll(i => (i as PkmconModel)?.Id == id);
                else if (typeof(T) == typeof(PkmskuModel))
                    items.RemoveAll(i => (i as PkmskuModel)?.Id == id);

                await SaveAllToLocalStorageAsync(key, items);
            }
            catch (Exception ex)
            {
                Console.WriteLine($"删除本地数据失败: {ex.Message}");
            }
        }

        #endregion
    }
}
