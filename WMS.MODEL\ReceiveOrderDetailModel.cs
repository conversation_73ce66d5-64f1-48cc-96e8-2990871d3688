namespace WMS.MODEL
{
    /// <summary>
    /// 收货单明细表 - RECCON
    /// </summary>
    public class RecconModel
    {
        public string Id { get; set; } = Guid.NewGuid().ToString();
        public string OrderId { get; set; }
        public int LineNumber { get; set; }

        /// <summary>
        /// 客户订单号 - CON
        /// </summary>
        public string CON { get; set; }

        /// <summary>
        /// 系统订单号 - ORD
        /// </summary>
        public string ORD { get; set; }

        /// <summary>
        /// 商品编号 - SKUCOD
        /// </summary>
        public string SKUCOD { get; set; }

        /// <summary>
        /// 商品名称 - SKUDES
        /// </summary>
        public string SKUDES { get; set; }

        /// <summary>
        /// 商品批次 - LOT
        /// </summary>
        public string LOT { get; set; }

        /// <summary>
        /// 商品LPN号 - LPN
        /// </summary>
        public string LPN { get; set; }

        /// <summary>
        /// 库位号 - LOC
        /// </summary>
        public string LOC { get; set; }

        /// <summary>
        /// 商品数量 - QTY
        /// </summary>
        public decimal QTY { get; set; }

        /// <summary>
        /// 待操作数量 - CAN
        /// </summary>
        public decimal CAN { get; set; }

        /// <summary>
        /// 已操作数量
        /// </summary>
        public decimal ActualQuantity { get; set; }

        /// <summary>
        /// 商品属性 - ATR
        /// </summary>
        public string ATR { get; set; }

        /// <summary>
        /// 商品类型 - TYP
        /// </summary>
        public string TYP { get; set; }

        public string Unit { get; set; } = "个";
        public string Remarks { get; set; }
    }
}