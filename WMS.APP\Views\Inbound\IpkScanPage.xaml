<?xml version="1.0" encoding="utf-8" ?>
<ContentPage
    x:Class="WMS.APP.Views.inbound.IpkScanPage"
    xmlns="http://schemas.microsoft.com/dotnet/2021/maui"
    xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
    xmlns:usercontrol="clr-namespace:WMS.APP.UserControl"
    Title="拣货扫描">

    <ContentPage.Content>
        <VerticalStackLayout Padding="10" Spacing="15">
            <!-- 订单信息区域 -->
            <Frame Padding="10" CornerRadius="8" BackgroundColor="#f5f5f5">
                <Grid RowDefinitions="Auto,Auto" ColumnDefinitions="Auto,*" RowSpacing="10" ColumnSpacing="10">
                    <Label Grid.Row="0" Grid.Column="0" Text="订单号：" FontAttributes="Bold" />
                    <Label Grid.Row="0" Grid.Column="1" Text="{Binding Order.OrderNumber}" />
                    
                    <Label Grid.Row="1" Grid.Column="0" Text="状态：" FontAttributes="Bold" />
                    <Label Grid.Row="1" Grid.Column="1" Text="{Binding Order.Status}" />
                </Grid>
            </Frame>
            
            <!-- 扫描区域 -->
            <Frame Padding="10" CornerRadius="8" BackgroundColor="White" BorderColor="#e0e0e0">
                <VerticalStackLayout Spacing="15">
                    <!-- 订单号扫描 -->
                    <usercontrol:ScanEntryView 
                        Title="订单号" 
                        ScanText="{Binding OrderNumber, Mode=TwoWay}" 
                        IsActive="{Binding IsOrderNumberActive}" 
                        ControlId="OrderNumber" />
                    
                    <!-- 商品条码扫描 -->
                    <usercontrol:ScanEntryView 
                        Title="商品条码" 
                        ScanText="{Binding ProductCode, Mode=TwoWay}" 
                        IsActive="{Binding IsProductCodeActive}" 
                        ControlId="ProductCode" />
                    
                    <!-- 库位号扫描 -->
                    <usercontrol:ScanEntryView 
                        Title="库位号" 
                        ScanText="{Binding LocationCode, Mode=TwoWay}" 
                        IsActive="{Binding IsLocationCodeActive}" 
                        ControlId="LocationCode" />
                    
                    <!-- 数量输入 -->
                    <usercontrol:ScanEntryView 
                        Title="拣货数量" 
                        ScanText="{Binding Quantity, Mode=TwoWay}" 
                        IsActive="{Binding IsQuantityActive}" 
                        IsFinalManualEntry="True"
                        ControlId="Quantity" />
                </VerticalStackLayout>
            </Frame>
            
            <!-- 商品信息区域 -->
            <Frame Padding="10" CornerRadius="8" BackgroundColor="#f8f8f8" IsVisible="{Binding IsProductInfoVisible}">
                <Grid RowDefinitions="Auto,Auto,Auto" ColumnDefinitions="Auto,*" RowSpacing="8" ColumnSpacing="10">
                    <Label Grid.Row="0" Grid.Column="0" Text="商品名称：" FontAttributes="Bold" />
                    <Label Grid.Row="0" Grid.Column="1" Text="{Binding CurrentProduct.ProductName}" />
                    
                    <Label Grid.Row="1" Grid.Column="0" Text="计划数量：" FontAttributes="Bold" />
                    <Label Grid.Row="1" Grid.Column="1" Text="{Binding CurrentProduct.PlannedQuantity}" />
                    
                    <Label Grid.Row="2" Grid.Column="0" Text="已拣数量：" FontAttributes="Bold" />
                    <Label Grid.Row="2" Grid.Column="1" Text="{Binding CurrentProduct.ActualQuantity}" />
                </Grid>
            </Frame>
            
            <!-- 操作按钮 -->
            <Button Text="提交" Command="{Binding SubmitCommand}" HorizontalOptions="Fill" />
            
            <!-- 状态信息 -->
            <Label Text="{Binding StatusMessage}" TextColor="{Binding StatusColor}" HorizontalOptions="Center" />
        </VerticalStackLayout>
    </ContentPage.Content>
</ContentPage>