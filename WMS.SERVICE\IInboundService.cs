﻿using WMS.MODEL;

namespace WMS.SERVICE
{
    /// <summary>
    /// 入库服务接口
    /// </summary>
    public interface IInboundService
    {
        #region 收货管理 (REC)

        // 收货单主表操作
        Task<List<ReceiveOrderModel>> GetRecordListAsync(string keyword = null, int page = 1, int pageSize = 20);
        Task<ReceiveOrderModel> GetRecordByIdAsync(string id);
        Task<ReceiveOrderModel> GetRecordByOrderAsync(string ord, string con = null);
        Task<bool> SaveRecordAsync(ReceiveOrderModel record);
        Task<bool> DeleteRecordAsync(string id);

        // 收货单明细操作
        Task<List<RecconModel>> GetRecconListAsync(string recordId);
        Task<RecconModel> GetRecconByIdAsync(string id);
        Task<bool> SaveRecconAsync(RecconModel reccon);
        Task<bool> UpdateRecconQuantityAsync(string id, decimal actualQuantity);

        // 收货单SKU明细操作
        Task<List<RecskuModel>> GetRecskuListAsync(string recordId);
        Task<RecskuModel> GetRecskuByIdAsync(string id);
        Task<bool> SaveRecskuAsync(RecskuModel recsku);
        Task<bool> UpdateRecskuQuantityAsync(string id, decimal actualQuantity);

        // 收货扫描相关
        Task<ScanResultModel> ProcessRecScanAsync(string ord, string skuCod, string loc, decimal qty);
        Task<bool> CompleteRecScanAsync(string recordId);

        #endregion

        #region 上架管理 (PKI)

        // 上架单主表操作
        Task<List<PkiModel>> GetPkiListAsync(string keyword = null, int page = 1, int pageSize = 20);
        Task<PkiModel> GetPkiByIdAsync(string id);
        Task<bool> SavePkiAsync(PkiModel pki);
        Task<bool> DeletePkiAsync(string id);

        // 上架单明细操作
        Task<List<PkiconModel>> GetPkiconListAsync(string pkiId);
        Task<bool> SavePkiconAsync(PkiconModel pkicon);
        Task<bool> UpdatePkiconQuantityAsync(string id, decimal actualQuantity);

        // 上架单SKU明细操作
        Task<List<PkiskuModel>> GetPkiskuListAsync(string pkiId);
        Task<bool> SavePkiskuAsync(PkiskuModel pkisku);
        Task<bool> UpdatePkiskuQuantityAsync(string id, decimal actualQuantity);

        // 上架扫描相关
        Task<ScanResultModel> ProcessPkiScanAsync(string ord, string skuCod, string sourceLoc, string targetLoc, decimal qty);
        Task<bool> CompletePkiScanAsync(string pkiId);

        #endregion

        #region 入库管理 (IPK)

        // 入库单主表操作
        Task<List<IpkModel>> GetIpkListAsync(string keyword = null, int page = 1, int pageSize = 20);
        Task<IpkModel> GetIpkByIdAsync(string id);
        Task<bool> SaveIpkAsync(IpkModel ipk);
        Task<bool> DeleteIpkAsync(string id);

        // 入库单明细操作
        Task<List<IpkconModel>> GetIpkconListAsync(string ipkId);
        Task<bool> SaveIpkconAsync(IpkconModel ipkcon);
        Task<bool> UpdateIpkconQuantityAsync(string id, decimal actualQuantity);

        // 入库单SKU明细操作
        Task<List<IpkskuModel>> GetIpkskuListAsync(string ipkId);
        Task<bool> SaveIpkskuAsync(IpkskuModel ipksku);
        Task<bool> UpdateIpkskuQuantityAsync(string id, decimal actualQuantity);

        // 入库扫描相关
        Task<ScanResultModel> ProcessIpkScanAsync(string ord, string skuCod, string loc, decimal qty);
        Task<bool> CompleteIpkScanAsync(string ipkId);

        #endregion

        #region 通用方法

        // 清理临时存储
        Task ClearTempStorageAsync();

        // 获取统计信息
        Task<Dictionary<string, int>> GetInboundStatisticsAsync();

        #endregion
    }
}
