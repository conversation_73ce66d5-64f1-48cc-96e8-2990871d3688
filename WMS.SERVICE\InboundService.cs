using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Text.Json;
using System.Threading.Tasks;
using WMS.MODEL;
using WMS.SERVICE.Utils;

namespace WMS.SERVICE
{
    /// <summary>
    /// 入库服务实现
    /// </summary>
    public class InboundService : IInboundService
    {
        private const string RECORD_KEY = "records";
        private const string RECCON_KEY = "reccons";
        private const string RECSKU_KEY = "recskus";
        private const string PKI_KEY = "pkis";
        private const string IPK_KEY = "ipks";
        
        private readonly ApiHelperr _apiHelper;

        public InboundService()
        {
            _apiHelper = new ApiHelperr();
        }

        #region 收货管理 (REC)

        public async Task<List<RecordModel>> GetRecordListAsync(string keyword = null, int page = 1, int pageSize = 20)
        {
            try
            {
                // 优先从API获取数据
                var apiUrl = $"/api/inbound/records?keyword={keyword}&page={page}&pageSize={pageSize}";
                var result = await _apiHelper.GetAsync<List<RecordModel>>(apiUrl);
                
                if (result != null)
                    return result;
            }
            catch (Exception ex)
            {
                // API调用失败，使用本地存储
                Console.WriteLine($"API调用失败，使用本地数据: {ex.Message}");
            }

            // 从本地存储获取数据
            var records = await GetStoredDataAsync<RecordModel>(RECORD_KEY);
            
            if (!string.IsNullOrWhiteSpace(keyword))
            {
                keyword = keyword.ToLower();
                records = records.Where(r => 
                    r.CON?.ToLower().Contains(keyword) == true ||
                    r.ORD?.ToLower().Contains(keyword) == true
                ).ToList();
            }

            return records
                .OrderByDescending(r => r.CreateDate)
                .Skip((page - 1) * pageSize)
                .Take(pageSize)
                .ToList();
        }

        public async Task<RecordModel> GetRecordByIdAsync(string id)
        {
            try
            {
                var apiUrl = $"/api/inbound/records/{id}";
                var result = await _apiHelper.GetAsync<RecordModel>(apiUrl);
                if (result != null)
                    return result;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"API调用失败: {ex.Message}");
            }

            var records = await GetStoredDataAsync<RecordModel>(RECORD_KEY);
            return records.FirstOrDefault(r => r.Id == id);
        }

        public async Task<RecordModel> GetRecordByOrderAsync(string ord, string con = null)
        {
            try
            {
                var apiUrl = $"/api/inbound/records/byorder?ord={ord}&con={con}";
                var result = await _apiHelper.GetAsync<RecordModel>(apiUrl);
                if (result != null)
                    return result;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"API调用失败: {ex.Message}");
            }

            var records = await GetStoredDataAsync<RecordModel>(RECORD_KEY);
            return records.FirstOrDefault(r => r.ORD == ord && (con == null || r.CON == con));
        }

        public async Task<bool> SaveRecordAsync(RecordModel record)
        {
            try
            {
                var apiUrl = "/api/inbound/records";
                var result = await _apiHelper.PostAsync<RecordModel, bool>(apiUrl, record);
                if (result)
                {
                    // 同步到本地存储
                    await SaveToLocalStorageAsync(RECORD_KEY, record);
                    return true;
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"API调用失败: {ex.Message}");
            }

            // API失败时保存到本地
            await SaveToLocalStorageAsync(RECORD_KEY, record);
            return true;
        }

        public async Task<bool> DeleteRecordAsync(string id)
        {
            try
            {
                var apiUrl = $"/api/inbound/records/{id}";
                var result = await _apiHelper.DeleteAsync(apiUrl);
                if (result)
                {
                    await RemoveFromLocalStorageAsync<RecordModel>(RECORD_KEY, id);
                    return true;
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"API调用失败: {ex.Message}");
            }

            await RemoveFromLocalStorageAsync<RecordModel>(RECORD_KEY, id);
            return true;
        }

        public async Task<List<RecconModel>> GetRecconListAsync(string recordId)
        {
            try
            {
                var apiUrl = $"/api/inbound/reccons?recordId={recordId}";
                var result = await _apiHelper.GetAsync<List<RecconModel>>(apiUrl);
                if (result != null)
                    return result;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"API调用失败: {ex.Message}");
            }

            var reccons = await GetStoredDataAsync<RecconModel>(RECCON_KEY);
            return reccons.Where(r => r.OrderId == recordId).ToList();
        }

        public async Task<RecconModel> GetRecconByIdAsync(string id)
        {
            try
            {
                var apiUrl = $"/api/inbound/reccons/{id}";
                var result = await _apiHelper.GetAsync<RecconModel>(apiUrl);
                if (result != null)
                    return result;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"API调用失败: {ex.Message}");
            }

            var reccons = await GetStoredDataAsync<RecconModel>(RECCON_KEY);
            return reccons.FirstOrDefault(r => r.Id == id);
        }

        public async Task<bool> SaveRecconAsync(RecconModel reccon)
        {
            try
            {
                var apiUrl = "/api/inbound/reccons";
                var result = await _apiHelper.PostAsync<RecconModel, bool>(apiUrl, reccon);
                if (result)
                {
                    await SaveToLocalStorageAsync(RECCON_KEY, reccon);
                    return true;
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"API调用失败: {ex.Message}");
            }

            await SaveToLocalStorageAsync(RECCON_KEY, reccon);
            return true;
        }

        public async Task<bool> UpdateRecconQuantityAsync(string id, decimal actualQuantity)
        {
            try
            {
                var apiUrl = $"/api/inbound/reccons/{id}/quantity";
                var result = await _apiHelper.PutAsync<decimal, bool>(apiUrl, actualQuantity);
                if (result)
                {
                    // 更新本地存储
                    var reccons = await GetStoredDataAsync<RecconModel>(RECCON_KEY);
                    var reccon = reccons.FirstOrDefault(r => r.Id == id);
                    if (reccon != null)
                    {
                        reccon.ActualQuantity = actualQuantity;
                        await SaveAllToLocalStorageAsync(RECCON_KEY, reccons);
                    }
                    return true;
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"API调用失败: {ex.Message}");
            }

            // 本地更新
            var localReccons = await GetStoredDataAsync<RecconModel>(RECCON_KEY);
            var localReccon = localReccons.FirstOrDefault(r => r.Id == id);
            if (localReccon != null)
            {
                localReccon.ActualQuantity = actualQuantity;
                await SaveAllToLocalStorageAsync(RECCON_KEY, localReccons);
                return true;
            }

            return false;
        }

        public async Task<List<RecskuModel>> GetRecskuListAsync(string recordId)
        {
            try
            {
                var apiUrl = $"/api/inbound/recskus?recordId={recordId}";
                var result = await _apiHelper.GetAsync<List<RecskuModel>>(apiUrl);
                if (result != null)
                    return result;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"API调用失败: {ex.Message}");
            }

            var recskus = await GetStoredDataAsync<RecskuModel>(RECSKU_KEY);
            return recskus.Where(r => r.OrderId == recordId).ToList();
        }

        public async Task<RecskuModel> GetRecskuByIdAsync(string id)
        {
            try
            {
                var apiUrl = $"/api/inbound/recskus/{id}";
                var result = await _apiHelper.GetAsync<RecskuModel>(apiUrl);
                if (result != null)
                    return result;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"API调用失败: {ex.Message}");
            }

            var recskus = await GetStoredDataAsync<RecskuModel>(RECSKU_KEY);
            return recskus.FirstOrDefault(r => r.Id == id);
        }

        public async Task<bool> SaveRecskuAsync(RecskuModel recsku)
        {
            try
            {
                var apiUrl = "/api/inbound/recskus";
                var result = await _apiHelper.PostAsync<RecskuModel, bool>(apiUrl, recsku);
                if (result)
                {
                    await SaveToLocalStorageAsync(RECSKU_KEY, recsku);
                    return true;
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"API调用失败: {ex.Message}");
            }

            await SaveToLocalStorageAsync(RECSKU_KEY, recsku);
            return true;
        }

        public async Task<bool> UpdateRecskuQuantityAsync(string id, decimal actualQuantity)
        {
            try
            {
                var apiUrl = $"/api/inbound/recskus/{id}/quantity";
                var result = await _apiHelper.PutAsync<decimal, bool>(apiUrl, actualQuantity);
                if (result)
                {
                    var recskus = await GetStoredDataAsync<RecskuModel>(RECSKU_KEY);
                    var recsku = recskus.FirstOrDefault(r => r.Id == id);
                    if (recsku != null)
                    {
                        recsku.ActualQuantity = actualQuantity;
                        await SaveAllToLocalStorageAsync(RECSKU_KEY, recskus);
                    }
                    return true;
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"API调用失败: {ex.Message}");
            }

            var localRecskus = await GetStoredDataAsync<RecskuModel>(RECSKU_KEY);
            var localRecsku = localRecskus.FirstOrDefault(r => r.Id == id);
            if (localRecsku != null)
            {
                localRecsku.ActualQuantity = actualQuantity;
                await SaveAllToLocalStorageAsync(RECSKU_KEY, localRecskus);
                return true;
            }

            return false;
        }

        public async Task<ScanResultModel> ProcessRecScanAsync(string ord, string skuCod, string loc, decimal qty)
        {
            try
            {
                var scanData = new
                {
                    ORD = ord,
                    SKUCOD = skuCod,
                    LOC = loc,
                    QTY = qty
                };

                var apiUrl = "/api/inbound/scan/rec";
                var result = await _apiHelper.PostAsync<object, ScanResultModel>(apiUrl, scanData);
                if (result != null)
                    return result;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"API调用失败: {ex.Message}");
            }

            // 本地处理逻辑
            return new ScanResultModel
            {
                IsSuccess = true,
                Message = "扫描成功（本地处理）",
                ScannedValue = $"{ord}-{skuCod}-{loc}-{qty}",
                NextFieldId = "Quantity"
            };
        }

        public async Task<bool> CompleteRecScanAsync(string recordId)
        {
            try
            {
                var apiUrl = $"/api/inbound/scan/rec/{recordId}/complete";
                var result = await _apiHelper.PostAsync<object, bool>(apiUrl, null);
                return result;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"API调用失败: {ex.Message}");
                return true; // 本地处理认为成功
            }
        }

        #endregion

        #region 上架管理 (PKI)

        public async Task<List<PkiModel>> GetPkiListAsync(string keyword = null, int page = 1, int pageSize = 20)
        {
            try
            {
                var apiUrl = $"/api/inbound/pkis?keyword={keyword}&page={page}&pageSize={pageSize}";
                var result = await _apiHelper.GetAsync<List<PkiModel>>(apiUrl);
                if (result != null)
                    return result;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"API调用失败: {ex.Message}");
            }

            var pkis = await GetStoredDataAsync<PkiModel>(PKI_KEY);

            if (!string.IsNullOrWhiteSpace(keyword))
            {
                keyword = keyword.ToLower();
                pkis = pkis.Where(p =>
                    p.CON?.ToLower().Contains(keyword) == true ||
                    p.ORD?.ToLower().Contains(keyword) == true
                ).ToList();
            }

            return pkis
                .OrderByDescending(p => p.CreateDate)
                .Skip((page - 1) * pageSize)
                .Take(pageSize)
                .ToList();
        }

        public async Task<PkiModel> GetPkiByIdAsync(string id)
        {
            try
            {
                var apiUrl = $"/api/inbound/pkis/{id}";
                var result = await _apiHelper.GetAsync<PkiModel>(apiUrl);
                if (result != null)
                    return result;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"API调用失败: {ex.Message}");
            }

            var pkis = await GetStoredDataAsync<PkiModel>(PKI_KEY);
            return pkis.FirstOrDefault(p => p.Id == id);
        }

        public async Task<bool> SavePkiAsync(PkiModel pki)
        {
            try
            {
                var apiUrl = "/api/inbound/pkis";
                var result = await _apiHelper.PostAsync<PkiModel, bool>(apiUrl, pki);
                if (result)
                {
                    await SaveToLocalStorageAsync(PKI_KEY, pki);
                    return true;
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"API调用失败: {ex.Message}");
            }

            await SaveToLocalStorageAsync(PKI_KEY, pki);
            return true;
        }

        public async Task<bool> DeletePkiAsync(string id)
        {
            try
            {
                var apiUrl = $"/api/inbound/pkis/{id}";
                var result = await _apiHelper.DeleteAsync(apiUrl);
                if (result)
                {
                    await RemoveFromLocalStorageAsync<PkiModel>(PKI_KEY, id);
                    return true;
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"API调用失败: {ex.Message}");
            }

            await RemoveFromLocalStorageAsync<PkiModel>(PKI_KEY, id);
            return true;
        }

        public async Task<List<PkiconModel>> GetPkiconListAsync(string pkiId)
        {
            // 实现类似的逻辑
            return new List<PkiconModel>();
        }

        public async Task<bool> SavePkiconAsync(PkiconModel pkicon)
        {
            // 实现保存逻辑
            return true;
        }

        public async Task<bool> UpdatePkiconQuantityAsync(string id, decimal actualQuantity)
        {
            // 实现更新数量逻辑
            return true;
        }

        public async Task<List<PkiskuModel>> GetPkiskuListAsync(string pkiId)
        {
            // 实现获取SKU明细逻辑
            return new List<PkiskuModel>();
        }

        public async Task<bool> SavePkiskuAsync(PkiskuModel pkisku)
        {
            // 实现保存SKU明细逻辑
            return true;
        }

        public async Task<bool> UpdatePkiskuQuantityAsync(string id, decimal actualQuantity)
        {
            // 实现更新SKU数量逻辑
            return true;
        }

        public async Task<ScanResultModel> ProcessPkiScanAsync(string ord, string skuCod, string sourceLoc, string targetLoc, decimal qty)
        {
            try
            {
                var scanData = new
                {
                    ORD = ord,
                    SKUCOD = skuCod,
                    SOURCELOC = sourceLoc,
                    TARGETLOC = targetLoc,
                    QTY = qty
                };

                var apiUrl = "/api/inbound/scan/pki";
                var result = await _apiHelper.PostAsync<object, ScanResultModel>(apiUrl, scanData);
                if (result != null)
                    return result;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"API调用失败: {ex.Message}");
            }

            return new ScanResultModel
            {
                IsSuccess = true,
                Message = "上架扫描成功（本地处理）",
                ScannedValue = $"{ord}-{skuCod}-{sourceLoc}-{targetLoc}-{qty}",
                NextFieldId = "Quantity"
            };
        }

        public async Task<bool> CompletePkiScanAsync(string pkiId)
        {
            try
            {
                var apiUrl = $"/api/inbound/scan/pki/{pkiId}/complete";
                var result = await _apiHelper.PostAsync<object, bool>(apiUrl, null);
                return result;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"API调用失败: {ex.Message}");
                return true;
            }
        }

        #endregion

        #region 入库管理 (IPK)

        public async Task<List<IpkModel>> GetIpkListAsync(string keyword = null, int page = 1, int pageSize = 20)
        {
            // 实现入库单列表获取逻辑
            return new List<IpkModel>();
        }

        public async Task<IpkModel> GetIpkByIdAsync(string id)
        {
            // 实现根据ID获取入库单逻辑
            return null;
        }

        public async Task<bool> SaveIpkAsync(IpkModel ipk)
        {
            // 实现保存入库单逻辑
            return true;
        }

        public async Task<bool> DeleteIpkAsync(string id)
        {
            // 实现删除入库单逻辑
            return true;
        }

        public async Task<List<IpkconModel>> GetIpkconListAsync(string ipkId)
        {
            // 实现获取入库单明细逻辑
            return new List<IpkconModel>();
        }

        public async Task<bool> SaveIpkconAsync(IpkconModel ipkcon)
        {
            // 实现保存入库单明细逻辑
            return true;
        }

        public async Task<bool> UpdateIpkconQuantityAsync(string id, decimal actualQuantity)
        {
            // 实现更新入库单明细数量逻辑
            return true;
        }

        public async Task<List<IpkskuModel>> GetIpkskuListAsync(string ipkId)
        {
            // 实现获取入库单SKU明细逻辑
            return new List<IpkskuModel>();
        }

        public async Task<bool> SaveIpkskuAsync(IpkskuModel ipksku)
        {
            // 实现保存入库单SKU明细逻辑
            return true;
        }

        public async Task<bool> UpdateIpkskuQuantityAsync(string id, decimal actualQuantity)
        {
            // 实现更新入库单SKU数量逻辑
            return true;
        }

        public async Task<ScanResultModel> ProcessIpkScanAsync(string ord, string skuCod, string loc, decimal qty)
        {
            try
            {
                var scanData = new
                {
                    ORD = ord,
                    SKUCOD = skuCod,
                    LOC = loc,
                    QTY = qty
                };

                var apiUrl = "/api/inbound/scan/ipk";
                var result = await _apiHelper.PostAsync<object, ScanResultModel>(apiUrl, scanData);
                if (result != null)
                    return result;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"API调用失败: {ex.Message}");
            }

            return new ScanResultModel
            {
                IsSuccess = true,
                Message = "入库扫描成功（本地处理）",
                ScannedValue = $"{ord}-{skuCod}-{loc}-{qty}",
                NextFieldId = "Quantity"
            };
        }

        public async Task<bool> CompleteIpkScanAsync(string ipkId)
        {
            try
            {
                var apiUrl = $"/api/inbound/scan/ipk/{ipkId}/complete";
                var result = await _apiHelper.PostAsync<object, bool>(apiUrl, null);
                return result;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"API调用失败: {ex.Message}");
                return true;
            }
        }

        #endregion

        #region 通用方法

        public async Task ClearTempStorageAsync()
        {
            try
            {
                SecureStorage.Remove(RECORD_KEY);
                SecureStorage.Remove(RECCON_KEY);
                SecureStorage.Remove(RECSKU_KEY);
                SecureStorage.Remove(PKI_KEY);
                SecureStorage.Remove(IPK_KEY);
            }
            catch (Exception ex)
            {
                Console.WriteLine($"清理临时存储失败: {ex.Message}");
            }
        }

        public async Task<Dictionary<string, int>> GetInboundStatisticsAsync()
        {
            try
            {
                var apiUrl = "/api/inbound/statistics";
                var result = await _apiHelper.GetAsync<Dictionary<string, int>>(apiUrl);
                if (result != null)
                    return result;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"API调用失败: {ex.Message}");
            }

            // 本地统计
            var records = await GetStoredDataAsync<RecordModel>(RECORD_KEY);
            return new Dictionary<string, int>
            {
                ["TotalRecords"] = records.Count,
                ["PendingRecords"] = records.Count(r => r.Status == "待收货"),
                ["CompletedRecords"] = records.Count(r => r.Status == "已完成")
            };
        }

        #endregion

        #region 私有辅助方法

        private async Task<List<T>> GetStoredDataAsync<T>(string key) where T : class
        {
            try
            {
                string json = await SecureStorage.GetAsync(key);
                if (string.IsNullOrEmpty(json))
                    return new List<T>();

                return JsonSerializer.Deserialize<List<T>>(json) ?? new List<T>();
            }
            catch (Exception ex)
            {
                Console.WriteLine($"获取本地数据失败: {ex.Message}");
                return new List<T>();
            }
        }

        private async Task SaveToLocalStorageAsync<T>(string key, T item) where T : class
        {
            try
            {
                var items = await GetStoredDataAsync<T>(key);

                // 查找现有项并更新或添加新项
                var existingIndex = -1;
                if (item is RecordModel record)
                    existingIndex = items.FindIndex(i => (i as RecordModel)?.Id == record.Id);
                else if (item is RecconModel reccon)
                    existingIndex = items.FindIndex(i => (i as RecconModel)?.Id == reccon.Id);
                else if (item is RecskuModel recsku)
                    existingIndex = items.FindIndex(i => (i as RecskuModel)?.Id == recsku.Id);

                if (existingIndex >= 0)
                    items[existingIndex] = item;
                else
                    items.Add(item);

                await SaveAllToLocalStorageAsync(key, items);
            }
            catch (Exception ex)
            {
                Console.WriteLine($"保存本地数据失败: {ex.Message}");
            }
        }

        private async Task SaveAllToLocalStorageAsync<T>(string key, List<T> items) where T : class
        {
            try
            {
                string json = JsonSerializer.Serialize(items);
                await SecureStorage.SetAsync(key, json);
            }
            catch (Exception ex)
            {
                Console.WriteLine($"保存所有本地数据失败: {ex.Message}");
            }
        }

        private async Task RemoveFromLocalStorageAsync<T>(string key, string id) where T : class
        {
            try
            {
                var items = await GetStoredDataAsync<T>(key);

                if (typeof(T) == typeof(RecordModel))
                    items.RemoveAll(i => (i as RecordModel)?.Id == id);
                else if (typeof(T) == typeof(RecconModel))
                    items.RemoveAll(i => (i as RecconModel)?.Id == id);
                else if (typeof(T) == typeof(RecskuModel))
                    items.RemoveAll(i => (i as RecskuModel)?.Id == id);

                await SaveAllToLocalStorageAsync(key, items);
            }
            catch (Exception ex)
            {
                Console.WriteLine($"删除本地数据失败: {ex.Message}");
            }
        }

        #endregion
    }
}
