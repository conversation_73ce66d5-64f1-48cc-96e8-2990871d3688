<?xml version="1.0" encoding="utf-8" ?>
<ContentPage xmlns="http://schemas.microsoft.com/dotnet/2021/maui"
             xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
             x:Class="WMS.APP.Views.outbound.PkmScanPage"
             xmlns:vm="clr-namespace:WMS.APP.ViewModels.outbound"
             xmlns:controls="clr-namespace:WMS.APP.UserControl"
             xmlns:converters="clr-namespace:WMS.APP.Common"
             Title="分拣扫描">
    <!--<ContentPage.Resources>
        <ResourceDictionary>
            <converters:BoolToColorConverter x:Key="BoolToColorConverter"/>
        </ResourceDictionary>
    </ContentPage.Resources>-->
    <!--<ContentPage.ToolbarItems>
        <ToolbarItem Text="返回" Command="{Binding BackCommand}" />
        <ToolbarItem Text="下一个" Command="{Binding BackCommand}" />
    </ContentPage.ToolbarItems>
    <ContentPage.MenuBarItems>
        <MenuBarItem Text="返回" />
        <MenuBarItem Text="下一个" />
    </ContentPage.MenuBarItems>-->

    <ContentPage.BindingContext>
        <vm:PkmScanViewModel />
    </ContentPage.BindingContext>


    <Grid RowDefinitions="*,Auto" Padding="10">
        <!-- 内容区，支持滚动 -->
        <ScrollView Grid.Row="0">
            <VerticalStackLayout>
                <controls:ScanEntryView x:Name="Order_Scan" Title="订单号" ControlId="Order_Scan" ScanText="{Binding OrderNumber}" />
                <controls:ScanEntryView x:Name="Sku_Scan" Title="物料编号" ControlId="Sku_Scan" ScanText="{Binding MaterialCode}" />
                <controls:ScanEntryView x:Name="Lot_Scan" Title="批次号" ControlId="Lot_Scan" ScanText="{Binding BatchNumber}" />
                <controls:ScanEntryView x:Name="Qty_Scan" Title="订单数量" ControlId="Qty_Scan" IsFinalManualEntry="True" ScanText="{Binding OrderQuantity}" />
            </VerticalStackLayout>
        </ScrollView>
        <!-- 固定在底部的提交按钮 -->
        <Button Grid.Row="1"
                Text="提交订单"
                Command="{Binding SubmitOrderCommand}"
                IsEnabled="{Binding CanSubmit}"
                Margin="0,16,0,0"/>
    </Grid>
</ContentPage>