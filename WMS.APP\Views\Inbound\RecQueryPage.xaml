<?xml version="1.0" encoding="utf-8" ?>
<ContentPage xmlns="http://schemas.microsoft.com/dotnet/2021/maui"
             xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
             x:Class="WMS.APP.Views.Inbound.RecQueryPage"
             Title="收货查询">
    
    <Grid RowDefinitions="Auto,*,Auto">
        <!-- 搜索区域 -->
        <Frame Grid.Row="0" Padding="15" Margin="10" CornerRadius="8" BackgroundColor="#F5F5F5">
            <VerticalStackLayout Spacing="10">
                <Label Text="收货单查询" FontSize="18" FontAttributes="Bold" HorizontalOptions="Center"/>
                
                <Grid ColumnDefinitions="*,Auto" ColumnSpacing="10">
                    <Entry Grid.Column="0" 
                           x:Name="searchEntry"
                           Placeholder="输入订单号或客户订单号"
                           Text="{Binding SearchKeyword}"
                           ReturnType="Search"
                           Completed="OnSearchCompleted"/>
                    <Button Grid.Column="1" 
                            Text="搜索" 
                            BackgroundColor="#2196F3" 
                            TextColor="White"
                            Command="{Binding SearchCommand}"/>
                </Grid>
                
                <!-- 快捷筛选 -->
                <Grid ColumnDefinitions="*,*,*" ColumnSpacing="5">
                    <Button Grid.Column="0" 
                            Text="全部" 
                            FontSize="12"
                            BackgroundColor="{Binding AllButtonColor}"
                            Command="{Binding FilterCommand}"
                            CommandParameter="All"/>
                    <Button Grid.Column="1" 
                            Text="待收货" 
                            FontSize="12"
                            BackgroundColor="{Binding PendingButtonColor}"
                            Command="{Binding FilterCommand}"
                            CommandParameter="Pending"/>
                    <Button Grid.Column="2" 
                            Text="已完成" 
                            FontSize="12"
                            BackgroundColor="{Binding CompletedButtonColor}"
                            Command="{Binding FilterCommand}"
                            CommandParameter="Completed"/>
                </Grid>
            </VerticalStackLayout>
        </Frame>
        
        <!-- 列表区域 -->
        <RefreshView Grid.Row="1" 
                     IsRefreshing="{Binding IsRefreshing}"
                     Command="{Binding RefreshCommand}">
            <CollectionView ItemsSource="{Binding Records}"
                            SelectionMode="Single"
                            SelectedItem="{Binding SelectedRecord}"
                            SelectionChangedCommand="{Binding SelectionChangedCommand}">
                <CollectionView.ItemTemplate>
                    <DataTemplate>
                        <Frame Padding="15" Margin="10,5" CornerRadius="8" BackgroundColor="White" HasShadow="True">
                            <Grid RowDefinitions="Auto,Auto,Auto,Auto" ColumnDefinitions="*,Auto" RowSpacing="8">
                                <!-- 订单号 -->
                                <Label Grid.Row="0" Grid.Column="0" 
                                       Text="{Binding ORD}" 
                                       FontSize="16" 
                                       FontAttributes="Bold"
                                       TextColor="#1976D2"/>
                                <Label Grid.Row="0" Grid.Column="1" 
                                       Text="{Binding Status}" 
                                       FontSize="12"
                                       BackgroundColor="{Binding StatusColor}"
                                       TextColor="White"
                                       Padding="8,4"
                                       HorizontalOptions="End"/>
                                
                                <!-- 客户订单号 -->
                                <Label Grid.Row="1" Grid.Column="0" Grid.ColumnSpan="2">
                                    <Label.FormattedText>
                                        <FormattedString>
                                            <Span Text="客户订单号: " FontAttributes="Bold"/>
                                            <Span Text="{Binding CON}"/>
                                        </FormattedString>
                                    </Label.FormattedText>
                                </Label>
                                
                                <!-- 供应商 -->
                                <Label Grid.Row="2" Grid.Column="0" Grid.ColumnSpan="2">
                                    <Label.FormattedText>
                                        <FormattedString>
                                            <Span Text="供应商: " FontAttributes="Bold"/>
                                            <Span Text="{Binding Supplier}"/>
                                        </FormattedString>
                                    </Label.FormattedText>
                                </Label>
                                
                                <!-- 统计信息 -->
                                <Grid Grid.Row="3" Grid.Column="0" Grid.ColumnSpan="2" 
                                      ColumnDefinitions="*,*,*" ColumnSpacing="10">
                                    <Label Grid.Column="0" 
                                           Text="{Binding TotalItems, StringFormat='总项目: {0}'}" 
                                           FontSize="12" 
                                           TextColor="Gray"/>
                                    <Label Grid.Column="1" 
                                           Text="{Binding TotalQuantity, StringFormat='总数量: {0}'}" 
                                           FontSize="12" 
                                           TextColor="Gray"/>
                                    <Label Grid.Column="2" 
                                           Text="{Binding CreateDate, StringFormat='{0:MM-dd HH:mm}'}" 
                                           FontSize="12" 
                                           TextColor="Gray"/>
                                </Grid>
                            </Grid>
                        </Frame>
                    </DataTemplate>
                </CollectionView.ItemTemplate>
                
                <CollectionView.EmptyView>
                    <VerticalStackLayout HorizontalOptions="Center" VerticalOptions="Center" Spacing="10">
                        <Label Text="📦" FontSize="48" HorizontalOptions="Center"/>
                        <Label Text="暂无收货单数据" FontSize="16" TextColor="Gray" HorizontalOptions="Center"/>
                        <Button Text="刷新" Command="{Binding RefreshCommand}" BackgroundColor="#2196F3" TextColor="White"/>
                    </VerticalStackLayout>
                </CollectionView.EmptyView>
            </CollectionView>
        </RefreshView>
        
        <!-- 底部操作区域 -->
        <Grid Grid.Row="2" ColumnDefinitions="*,*" ColumnSpacing="10" Padding="10">
            <Button Grid.Column="0" 
                    Text="新建收货单" 
                    BackgroundColor="#4CAF50" 
                    TextColor="White"
                    Command="{Binding CreateCommand}"/>
            <Button Grid.Column="1" 
                    Text="查看详情" 
                    BackgroundColor="#FF9800" 
                    TextColor="White"
                    Command="{Binding ViewDetailsCommand}"
                    IsEnabled="{Binding HasSelectedRecord}"/>
        </Grid>
    </Grid>
</ContentPage>
