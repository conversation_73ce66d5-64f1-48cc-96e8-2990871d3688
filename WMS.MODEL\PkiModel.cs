namespace WMS.MODEL
{
    /// <summary>
    /// 上架单主表 - PKI
    /// </summary>
    public class PkiModel
    {
        public string Id { get; set; } = Guid.NewGuid().ToString();
        
        /// <summary>
        /// 客户订单号 - CON
        /// </summary>
        public string CON { get; set; }
        
        /// <summary>
        /// 系统订单号 - ORD
        /// </summary>
        public string ORD { get; set; }
        
        public DateTime CreateDate { get; set; } = DateTime.Now;
        public string Status { get; set; } = "待上架";
        public string Remarks { get; set; }
        public string CreateUser { get; set; }
        
        // 汇总信息
        public int TotalItems { get; set; }
        public decimal TotalQuantity { get; set; }
        public int CompletedItems { get; set; }
    }
    
    /// <summary>
    /// 上架单明细表 - PKICON
    /// </summary>
    public class PkiconModel
    {
        public string Id { get; set; } = Guid.NewGuid().ToString();
        public string PkiId { get; set; }
        public int LineNumber { get; set; }
        
        /// <summary>
        /// 客户订单号 - CON
        /// </summary>
        public string CON { get; set; }
        
        /// <summary>
        /// 系统订单号 - ORD
        /// </summary>
        public string ORD { get; set; }
        
        /// <summary>
        /// 商品编号 - SKUCOD
        /// </summary>
        public string SKUCOD { get; set; }
        
        /// <summary>
        /// 商品名称 - SKUDES
        /// </summary>
        public string SKUDES { get; set; }
        
        /// <summary>
        /// 商品批次 - LOT
        /// </summary>
        public string LOT { get; set; }
        
        /// <summary>
        /// 商品LPN号 - LPN
        /// </summary>
        public string LPN { get; set; }
        
        /// <summary>
        /// 库位号 - LOC
        /// </summary>
        public string LOC { get; set; }
        
        /// <summary>
        /// 商品数量 - QTY
        /// </summary>
        public decimal QTY { get; set; }
        
        /// <summary>
        /// 待操作数量 - CAN
        /// </summary>
        public decimal CAN { get; set; }
        
        /// <summary>
        /// 已操作数量
        /// </summary>
        public decimal ActualQuantity { get; set; }
        
        /// <summary>
        /// 商品属性 - ATR
        /// </summary>
        public string ATR { get; set; }
        
        /// <summary>
        /// 商品类型 - TYP
        /// </summary>
        public string TYP { get; set; }
        
        public string Unit { get; set; } = "个";
        public string Remarks { get; set; }
        public DateTime CreateDate { get; set; } = DateTime.Now;
        public string Status { get; set; } = "待上架";
    }
    
    /// <summary>
    /// 上架单SKU明细表 - PKISKU
    /// </summary>
    public class PkiskuModel
    {
        public string Id { get; set; } = Guid.NewGuid().ToString();
        public string PkiId { get; set; }
        public int LineNumber { get; set; }
        
        /// <summary>
        /// 客户订单号 - CON
        /// </summary>
        public string CON { get; set; }
        
        /// <summary>
        /// 系统订单号 - ORD
        /// </summary>
        public string ORD { get; set; }
        
        /// <summary>
        /// 商品编号 - SKUCOD
        /// </summary>
        public string SKUCOD { get; set; }
        
        /// <summary>
        /// 商品名称 - SKUDES
        /// </summary>
        public string SKUDES { get; set; }
        
        /// <summary>
        /// 商品批次 - LOT
        /// </summary>
        public string LOT { get; set; }
        
        /// <summary>
        /// 商品LPN号 - LPN
        /// </summary>
        public string LPN { get; set; }
        
        /// <summary>
        /// 库位号 - LOC
        /// </summary>
        public string LOC { get; set; }
        
        /// <summary>
        /// 商品数量 - QTY
        /// </summary>
        public decimal QTY { get; set; }
        
        /// <summary>
        /// 待操作数量 - CAN
        /// </summary>
        public decimal CAN { get; set; }
        
        /// <summary>
        /// 已操作数量
        /// </summary>
        public decimal ActualQuantity { get; set; }
        
        /// <summary>
        /// 商品属性 - ATR
        /// </summary>
        public string ATR { get; set; }
        
        /// <summary>
        /// 商品类型 - TYP
        /// </summary>
        public string TYP { get; set; }
        
        public string Unit { get; set; } = "个";
        public string Remarks { get; set; }
        public DateTime CreateDate { get; set; } = DateTime.Now;
        public string Status { get; set; } = "待上架";
        public string SourceLoc { get; set; } // 来源库位
        public string TargetLoc { get; set; } // 目标库位
    }
}
