﻿using Android.Content;
using Android.Views;
using Android.Views.InputMethods;
using Android.Widget;
using Microsoft.Maui.Handlers;
using Microsoft.Maui.Platform;

namespace WMS.APP.Platforms.Android
{
    public static class EntryBehavior
    {
        public static void Init()
        {
            EntryHandler.Mapper.AppendToMapping("CustomCursorBehavior", (handler, view) =>
            {
                if (handler.PlatformView is EditText nativeEditText)
                {
                    nativeEditText.SetCursorVisible(false);// = false; // 默认隐藏光标

                    // 点击 Entry 显示光标
                    nativeEditText.Touch += (sender, e) =>
                    {
                        if (e.Event.Action == MotionEventActions.Down)
                        {
                            //nativeEditText.CursorVisible = true;
                            nativeEditText.SetCursorVisible(false);
                        }
                    };

                    // 输入完成或焦点丢失 → 隐藏光标
                    nativeEditText.FocusChange += (sender, e) =>
                    {
                        if (!nativeEditText.HasFocus)
                        {
                            //nativeEditText.CursorVisible = false;
                            nativeEditText.SetCursorVisible(false);
                        }
                    };

                    // 用户点击软键盘的“完成”按钮后隐藏光标
                    nativeEditText.EditorAction += (sender, e) =>
                    {
                        if (e.ActionId == ImeAction.Done || e.ActionId == ImeAction.Next)
                        {
                            nativeEditText.ClearFocus();
                            //nativeEditText.CursorVisible = false;
                            nativeEditText.SetCursorVisible(false);

                            // 隐藏软键盘
                            var inputMethodManager = nativeEditText.Context?.GetSystemService(Context.InputMethodService) as InputMethodManager;
                            inputMethodManager?.HideSoftInputFromWindow(nativeEditText.WindowToken, HideSoftInputFlags.None);
                        }
                    };

                    // 键盘关闭时隐藏光标
                    /*     var activity = Platform.CurrentActivity;
                         var rootView = activity?.Window?.DecorView?.RootView;

                         if (rootView != null)
                         {
                             int previousHeight = 0;
                             rootView.ViewTreeObserver?.AddOnGlobalLayoutListener(new GlobalLayoutListener(() =>
                             {
                                 Rect r = new();
                                 rootView.GetWindowVisibleDisplayFrame(r);
                                 int heightDiff = rootView.Height - ((int)r.Height);

                                 bool isKeyboardShown = heightDiff > rootView.Height * 0.15; // 判断是否显示键盘

                                 if (!isKeyboardShown && previousHeight != 0)
                                 {
                                     nativeEditText.SetCursorVisible(false);
                                 }

                                 previousHeight = heightDiff;
                             }));
                         }*/
                }
            });
        }
        private class GlobalLayoutListener : Java.Lang.Object, ViewTreeObserver.IOnGlobalLayoutListener
        {
            private readonly Action _onGlobalLayout;

            public GlobalLayoutListener(Action onGlobalLayout)
            {
                _onGlobalLayout = onGlobalLayout;
            }

            public void OnGlobalLayout() => _onGlobalLayout.Invoke();
        }
    }
}
