using CommunityToolkit.Mvvm.ComponentModel;
using CommunityToolkit.Mvvm.Input;
using System.Collections.ObjectModel;
using WMS.APP.Common;
using WMS.MODEL;
using WMS.SERVICE;

namespace WMS.APP.ViewModels.inbound
{
    //[QueryProperty(nameof(OrderParameter), "param")]
    public partial class ReceiveOrderDetailsViewModel : ObservableObject
    {
        private readonly INavigationService _navigationService;
        private readonly IIboundService _dataService;

        [ObservableProperty]
        private ReceiveOrderModel _currentOrder;

        [ObservableProperty]
        private ReceiveOrderDetailModel _currentDetail;

        [ObservableProperty]
        private int _currentIndex = 0;

        [ObservableProperty]
        private string _currentIndexDisplay;

        [ObservableProperty]
        private ObservableCollection<string> _unitOptions = new()
        {
            "个", "箱", "件", "千克", "吨"
        };

        public object OrderParameter
        {
            set
            {
                if (value is ReceiveOrderModel order)
                {
                    //Initialize(order);
                }
            }
        }

        public ReceiveOrderDetailsViewModel(INavigationService navigationService, IIboundService dataService)
        {
            _navigationService = navigationService;
            _dataService = dataService;
        }

        public void Initialize(ReceiveOrderModel order)
        {
            CurrentOrder = order;

            // 如果没有明细，添加一个空明细
            if (CurrentOrder.Details == null)
            {
                CurrentOrder.Details = new ObservableCollection<ReceiveOrderDetailModel>();
            }

            if (CurrentOrder.Details.Count == 0)
            {
                AddNewDetailCommand.Execute(null);
            }
            else
            {
                CurrentIndex = 0;
                UpdateCurrentDetail();
            }
        }

        [RelayCommand]
        private void NavigateUpDetail()
        {
            if (CurrentOrder == null || CurrentOrder.Details.Count == 0) return;

            CurrentIndex = (CurrentIndex - 1 + CurrentOrder.Details.Count) % CurrentOrder.Details.Count;
            UpdateCurrentDetail();
        }


        [RelayCommand]
        private void NavigateNxDetail()
        {
            if (CurrentOrder == null || CurrentOrder.Details.Count == 0) return;

            CurrentIndex = (CurrentIndex + 1 + CurrentOrder.Details.Count) % CurrentOrder.Details.Count;
            UpdateCurrentDetail();
        }

        [RelayCommand]
        private void AddNewDetail()
        {
            var newDetail = new ReceiveOrderDetailModel
            {
                OrderId = CurrentOrder.Id,
                LineNumber = CurrentOrder.Details.Count + 1
            };

            CurrentOrder.Details.Add(newDetail);
            CurrentIndex = CurrentOrder.Details.Count - 1;
            UpdateCurrentDetail();
        }

        [RelayCommand]
        private void DeleteCurrentDetail()
        {
            if (CurrentIndex >= 0 && CurrentOrder.Details.Count > 0)
            {
                CurrentOrder.Details.RemoveAt(CurrentIndex);

                // 重新编号
                for (int i = 0; i < CurrentOrder.Details.Count; i++)
                {
                    CurrentOrder.Details[i].LineNumber = i + 1;
                }

                if (CurrentOrder.Details.Count == 0)
                {
                    AddNewDetailCommand.Execute(null);
                }
                else
                {
                    CurrentIndex = Math.Min(CurrentIndex, CurrentOrder.Details.Count - 1);
                    UpdateCurrentDetail();
                }
            }
        }

        [RelayCommand]
        private async Task SaveDetails()
        {
            try
            {
                // 保存订单及明细
                await _dataService.SaveReceiveOrderAsync(CurrentOrder);
                await Shell.Current.DisplayAlert("成功", "订单明细保存成功", "确定");
            }
            catch (Exception ex)
            {
                await Shell.Current.DisplayAlert("错误", $"保存订单明细失败: {ex.Message}", "确定");
            }
        }

        private void UpdateCurrentDetail()
        {
            // 更新当前明细
            CurrentDetail = CurrentOrder.Details[CurrentIndex];

            // 更新索引显示（例如：1/5）
            CurrentIndexDisplay = $"{CurrentIndex + 1}/{CurrentOrder.Details.Count}";

            // 通知属性变更
            OnPropertyChanged(nameof(CurrentDetail));
            OnPropertyChanged(nameof(CurrentIndexDisplay));
        }

    }
}
