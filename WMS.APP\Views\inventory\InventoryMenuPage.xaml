<?xml version="1.0" encoding="utf-8" ?>
<ContentPage xmlns="http://schemas.microsoft.com/dotnet/2021/maui"
             xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
             x:Class="WMS.APP.Views.Inventory.InventoryMenuPage"
             Title="库存管理">
    
    <ScrollView>
        <VerticalStackLayout Padding="20" Spacing="15">
            <!-- 页面标题 -->
            <Label Text="库存管理" 
                   FontSize="24" 
                   FontAttributes="Bold" 
                   HorizontalOptions="Center" 
                   Margin="0,0,0,20"/>
            
            <!-- 库存操作模块 -->
            <Frame Padding="15" CornerRadius="10" BackgroundColor="#E0F2F1">
                <VerticalStackLayout Spacing="10">
                    <Label Text="库存操作" FontSize="18" FontAttributes="Bold" TextColor="#00695C"/>
                    <Grid ColumnDefinitions="*,*" RowDefinitions="Auto" ColumnSpacing="10" RowSpacing="10">
                        <!-- 移库操作 -->
                        <Button Grid.Row="0" Grid.Column="0" 
                                Text="移库操作" 
                                BackgroundColor="#009688" 
                                TextColor="White"
                                Clicked="OnInvScanClicked"/>
                        <!-- 库存查询 -->
                        <Button Grid.Row="0" Grid.Column="1" 
                                Text="库存查询" 
                                BackgroundColor="#009688" 
                                TextColor="White"
                                Clicked="OnInvQueryClicked"/>
                    </Grid>
                </VerticalStackLayout>
            </Frame>
            
            <!-- 库存统计 -->
            <Frame Padding="15" CornerRadius="10" BackgroundColor="#E8F5E8">
                <VerticalStackLayout Spacing="10">
                    <Label Text="库存统计" FontSize="16" FontAttributes="Bold" TextColor="#2E7D32"/>
                    <Grid ColumnDefinitions="*,*,*" RowDefinitions="Auto,Auto" ColumnSpacing="10" RowSpacing="5">
                        <Label Grid.Row="0" Grid.Column="0" Text="总库存" HorizontalOptions="Center" FontSize="12"/>
                        <Label Grid.Row="0" Grid.Column="1" Text="可用库存" HorizontalOptions="Center" FontSize="12"/>
                        <Label Grid.Row="0" Grid.Column="2" Text="冻结库存" HorizontalOptions="Center" FontSize="12"/>
                        
                        <Label Grid.Row="1" Grid.Column="0" Text="0" HorizontalOptions="Center" FontSize="18" FontAttributes="Bold" TextColor="#009688"/>
                        <Label Grid.Row="1" Grid.Column="1" Text="0" HorizontalOptions="Center" FontSize="18" FontAttributes="Bold" TextColor="#4CAF50"/>
                        <Label Grid.Row="1" Grid.Column="2" Text="0" HorizontalOptions="Center" FontSize="18" FontAttributes="Bold" TextColor="#FF9800"/>
                    </Grid>
                </VerticalStackLayout>
            </Frame>
            
            <!-- 快捷操作 -->
            <Frame Padding="15" CornerRadius="10" BackgroundColor="#F1F8E9">
                <VerticalStackLayout Spacing="10">
                    <Label Text="快捷操作" FontSize="16" FontAttributes="Bold" TextColor="#558B2F"/>
                    <Grid ColumnDefinitions="*,*" RowDefinitions="Auto" ColumnSpacing="10" RowSpacing="10">
                        <!-- 库存盘点 -->
                        <Button Grid.Row="0" Grid.Column="0" 
                                Text="库存盘点" 
                                BackgroundColor="#689F38" 
                                TextColor="White"
                                Clicked="OnInventoryCountClicked"/>
                        <!-- 库存报表 -->
                        <Button Grid.Row="0" Grid.Column="1" 
                                Text="库存报表" 
                                BackgroundColor="#689F38" 
                                TextColor="White"
                                Clicked="OnInventoryReportClicked"/>
                    </Grid>
                </VerticalStackLayout>
            </Frame>
        </VerticalStackLayout>
    </ScrollView>
</ContentPage>
