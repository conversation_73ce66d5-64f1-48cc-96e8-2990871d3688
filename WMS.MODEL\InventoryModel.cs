﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace WMS.MODEL
{
    /// <summary>
    /// 库存主表 - INV
    /// </summary>
    public class InvModel : INotifyPropertyChanged
    {
        public string Id { get; set; } = Guid.NewGuid().ToString();

        /// <summary>
        /// 商品编号 - SKUCOD
        /// </summary>
        public string SKUCOD { get; set; } = string.Empty;

        /// <summary>
        /// 商品名称 - SKUDES
        /// </summary>
        public string SKUDES { get; set; } = string.Empty;

        /// <summary>
        /// 商品批次 - LOT
        /// </summary>
        public string LOT { get; set; } = string.Empty;

        /// <summary>
        /// 商品LPN号 - LPN
        /// </summary>
        public string LPN { get; set; } = string.Empty;

        /// <summary>
        /// 库位号 - LOC
        /// </summary>
        public string LOC { get; set; } = string.Empty;

        /// <summary>
        /// 商品数量 - QTY
        /// </summary>
        public decimal QTY { get; set; }

        /// <summary>
        /// 待操作数量 - CAN
        /// </summary>
        public decimal CAN { get; set; }

        /// <summary>
        /// 商品属性 - ATR
        /// </summary>
        public string ATR { get; set; } = string.Empty;

        /// <summary>
        /// 商品类型 - TYP
        /// </summary>
        public string TYP { get; set; } = string.Empty;

        public string Unit { get; set; } = "个";
        public DateTime LastUpdateTime { get; set; } = DateTime.Now;
        public string Status { get; set; } = "正常";
        public string CreateUser { get; set; }
        public DateTime CreateDate { get; set; } = DateTime.Now;

        private bool _isSelected;
        public bool IsSelected
        {
            get => _isSelected;
            set
            {
                if (_isSelected != value)
                {
                    _isSelected = value;
                    OnPropertyChanged(nameof(IsSelected));
                }
            }
        }

        public event PropertyChangedEventHandler PropertyChanged;
        protected void OnPropertyChanged(string propertyName)
            => PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
    }

    /// <summary>
    /// 移库操作表 - INVTRANSFER
    /// </summary>
    public class InvTransferModel
    {
        public string Id { get; set; } = Guid.NewGuid().ToString();

        /// <summary>
        /// 商品编号 - SKUCOD
        /// </summary>
        public string SKUCOD { get; set; }

        /// <summary>
        /// 商品名称 - SKUDES
        /// </summary>
        public string SKUDES { get; set; }

        /// <summary>
        /// 商品批次 - LOT
        /// </summary>
        public string LOT { get; set; }

        /// <summary>
        /// 商品LPN号 - LPN
        /// </summary>
        public string LPN { get; set; }

        /// <summary>
        /// 源库位号 - SOURCELOC
        /// </summary>
        public string SOURCELOC { get; set; }

        /// <summary>
        /// 目标库位号 - TARGETLOC
        /// </summary>
        public string TARGETLOC { get; set; }

        /// <summary>
        /// 移库数量 - QTY
        /// </summary>
        public decimal QTY { get; set; }

        /// <summary>
        /// 商品属性 - ATR
        /// </summary>
        public string ATR { get; set; }

        /// <summary>
        /// 商品类型 - TYP
        /// </summary>
        public string TYP { get; set; }

        public string Unit { get; set; } = "个";
        public string Status { get; set; } = "待移库";
        public string CreateUser { get; set; }
        public DateTime CreateDate { get; set; } = DateTime.Now;
        public string Remarks { get; set; }
    }
}
