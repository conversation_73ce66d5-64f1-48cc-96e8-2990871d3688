﻿using System.Diagnostics;


namespace WMS.APP.Common
{
    public class NavigationService : INavigationService
    {
        public async Task NavigateToAsync(string route)
        {
            if (string.IsNullOrEmpty(route))
                throw new ArgumentNullException(nameof(route));

            try
            {
                await Shell.Current.GoToAsync(route);
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"导航错误: {ex.Message}");
                // 可以选择重新抛出异常或处理它
                throw;
            }
        }
        int a = 0;
        public async Task NavigateToAsync(string route, object parameternew)
        {
            a++;
            if (string.IsNullOrEmpty(route))
                throw new ArgumentNullException(nameof(route));

            try
            {
                if (parameternew is string id)
                {
                    // 如果参数是字符串，假设它是ID
                    await Shell.Current.GoToAsync($"{route}?orderId={id}");
                }
                else
                {
                    // 对于其他类型的参数，使用默认方式
                    var navigationParameter = new ShellNavigationQueryParameters
                    {
                        { "param", parameternew }
                    };
                    //await Shell.Current.GoToAsync(route, navigationParameter);
                    //await Shell.Current.GoToAsync(route);
                    await Shell.Current.GoToAsync(route, true, new Dictionary<string, object>{
                        { "param", parameternew }
                    });
                }
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"导航错误: {ex.Message}");
                throw;
            }
        }

        public async Task CloseCurrentAsync()
        {
            try
            {
                await Shell.Current.GoToAsync("..");
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"导航错误: {ex.Message}");
                throw;
            }
        }

        public async Task GoBackAsync()
        {
            try
            {
                await Shell.Current.GoToAsync("..");
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"导航错误: {ex.Message}");
                throw;
            }
        }
    }
}
