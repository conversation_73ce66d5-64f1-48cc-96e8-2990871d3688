using CommunityToolkit.Mvvm.Messaging;
using System.Runtime.Versioning;
using WMS.APP.Message;
using WMS.APP.Common;

namespace WMS.APP.UserControl;

public partial class ScanEntryView : ContentView
{
    private bool _isListeningToBroadcast = false;

    public ScanEntryView()
    {
        InitializeComponent();

        // 注册消息监听
        WeakReferenceMessenger.Default.Register<PdaBroadcastMessage>(this, OnPdaBroadcastReceived);
        WeakReferenceMessenger.Default.Register<ActivateScanFieldMessage>(this, OnActivateField);
        WeakReferenceMessenger.Default.Register<DeactivateScanFieldMessage>(this, OnDeactivateField);
        WeakReferenceMessenger.Default.Register<ClearScanFieldMessage>(this, OnClearField);
    }

    #region Bindable Properties

    public static readonly BindableProperty TitleProperty =
        BindableProperty.Create(nameof(Title), typeof(string), typeof(ScanEntryView), string.Empty);

    public string Title
    {
        get => (string)GetValue(TitleProperty);
        set => SetValue(TitleProperty, value);
    }

    public static readonly BindableProperty ScanTextProperty =
        BindableProperty.Create(nameof(ScanText), typeof(string), typeof(ScanEntryView), string.Empty, BindingMode.TwoWay);

    public string ScanText
    {
        get => (string)GetValue(ScanTextProperty);
        set => SetValue(ScanTextProperty, value);
    }

    public static readonly BindableProperty IsActiveProperty =
        BindableProperty.Create(nameof(IsActive), typeof(bool), typeof(ScanEntryView), false, propertyChanged: OnIsActiveChanged);

    public bool IsActive
    {
        get => (bool)GetValue(IsActiveProperty);
        set => SetValue(IsActiveProperty, value);
    }

    public static readonly BindableProperty IsFinalManualEntryProperty =
        BindableProperty.Create(nameof(IsFinalManualEntry), typeof(bool), typeof(ScanEntryView), false);

    public bool IsFinalManualEntry
    {
        get => (bool)GetValue(IsFinalManualEntryProperty);
        set => SetValue(IsFinalManualEntryProperty, value);
    }

    public static readonly BindableProperty PlaceholderProperty =
        BindableProperty.Create(nameof(Placeholder), typeof(string), typeof(ScanEntryView), "请输入或扫码");

    public string Placeholder
    {
        get => (string)GetValue(PlaceholderProperty);
        set => SetValue(PlaceholderProperty, value);
    }

    public static readonly BindableProperty BorderColorProperty =
        BindableProperty.Create(nameof(BorderColor), typeof(Color), typeof(ScanEntryView), Colors.LightGray);

    public Color BorderColor
    {
        get => (Color)GetValue(BorderColorProperty);
        set => SetValue(BorderColorProperty, value);
    }

    public static readonly BindableProperty BackgroundColorProperty =
        BindableProperty.Create(nameof(BackgroundColor), typeof(Color), typeof(ScanEntryView), Colors.White);

    public new Color BackgroundColor
    {
        get => (Color)GetValue(BackgroundColorProperty);
        set => SetValue(BackgroundColorProperty, value);
    }

    public static readonly BindableProperty TitleColorProperty =
        BindableProperty.Create(nameof(TitleColor), typeof(Color), typeof(ScanEntryView), Colors.Black);

    public Color TitleColor
    {
        get => (Color)GetValue(TitleColorProperty);
        set => SetValue(TitleColorProperty, value);
    }

    public static readonly BindableProperty StatusTextProperty =
        BindableProperty.Create(nameof(StatusText), typeof(string), typeof(ScanEntryView), string.Empty);

    public string StatusText
    {
        get => (string)GetValue(StatusTextProperty);
        set => SetValue(StatusTextProperty, value);
    }

    public static readonly BindableProperty StatusColorProperty =
        BindableProperty.Create(nameof(StatusColor), typeof(Color), typeof(ScanEntryView), Colors.Gray);

    public Color StatusColor
    {
        get => (Color)GetValue(StatusColorProperty);
        set => SetValue(StatusColorProperty, value);
    }

    public static readonly BindableProperty ShowStatusProperty =
        BindableProperty.Create(nameof(ShowStatus), typeof(bool), typeof(ScanEntryView), false);

    public bool ShowStatus
    {
        get => (bool)GetValue(ShowStatusProperty);
        set => SetValue(ShowStatusProperty, value);
    }

    public static readonly BindableProperty IsReadOnlyProperty =
        BindableProperty.Create(nameof(IsReadOnly), typeof(bool), typeof(ScanEntryView), false);

    public bool IsReadOnly
    {
        get => (bool)GetValue(IsReadOnlyProperty);
        set => SetValue(IsReadOnlyProperty, value);
    }

    public string ControlId { get; set; }

    #endregion

    #region Event Handlers and Methods

    private static void OnIsActiveChanged(BindableObject bindable, object oldVal, object newVal)
    {
        var control = (ScanEntryView)bindable;
        control.UpdateActiveState((bool)newVal);
    }

    private void UpdateActiveState(bool isActive)
    {
        MainThread.BeginInvokeOnMainThread(() =>
        {
            if (isActive)
            {
                // 激活状态：高亮显示
                BorderColor = Colors.Orange;
                BackgroundColor = Color.FromArgb("#FFF8E1");
                TitleColor = Colors.Orange;
                StatusText = "当前扫描位置";
                StatusColor = Colors.Orange;
                ShowStatus = true;

                // 开始监听PDA广播
                _isListeningToBroadcast = true;

                // 如果是最后的手动输入字段，设置数字键盘
                if (IsFinalManualEntry)
                {
                    entry.Keyboard = Keyboard.Numeric;
                    entry.Focus(); // 手动输入时自动获取焦点
                }
            }
            else
            {
                // 非激活状态：恢复默认样式
                BorderColor = Colors.LightGray;
                BackgroundColor = Colors.White;
                TitleColor = Colors.Black;
                ShowStatus = false;

                // 停止监听PDA广播
                _isListeningToBroadcast = false;

                // 如果有值，显示完成状态
                if (!string.IsNullOrEmpty(ScanText))
                {
                    BorderColor = Colors.Green;
                    BackgroundColor = Color.FromArgb("#E8F5E8");
                }
            }
        });
    }

    private void OnPdaBroadcastReceived(object recipient, PdaBroadcastMessage message)
    {
        // 只有当前激活的控件才处理PDA广播
        if (_isListeningToBroadcast && IsActive)
        {
            MainThread.BeginInvokeOnMainThread(() =>
            {
                ScanText = message.ScanData;
                ProcessScanData(message.ScanData, false);
            });
        }
    }

    private void OnActivateField(object recipient, ActivateScanFieldMessage message)
    {
        if (message.FieldId == ControlId)
        {
            IsActive = true;
        }
    }

    private void OnDeactivateField(object recipient, DeactivateScanFieldMessage message)
    {
        if (message.FieldId == ControlId)
        {
            IsActive = false;
        }
    }

    private void OnClearField(object recipient, ClearScanFieldMessage message)
    {
        if (message.FieldId == ControlId)
        {
            ClearEntry();
        }
    }

    private async void Entry_Completed(object sender, EventArgs e)
    {
        if (!string.IsNullOrEmpty(ScanText))
        {
            await ProcessScanData(ScanText, true);
        }
    }

    private void OnTextChanged(object sender, TextChangedEventArgs e)
    {
        // 文本变化时更新状态
        if (!string.IsNullOrEmpty(e.NewTextValue))
        {
            StatusText = "数据已输入";
            StatusColor = Colors.Green;
        }
        else
        {
            StatusText = IsActive ? "当前扫描位置" : "";
            StatusColor = Colors.Orange;
        }
    }

    private async Task ProcessScanData(string scanData, bool isManualInput)
    {
        try
        {
            // 延迟处理，确保UI更新完成
            await Task.Delay(100);

            // 发送扫描完成消息
            var message = new ScanCompletedMessage(ControlId, IsFinalManualEntry, scanData)
            {
                IsManualInput = isManualInput
            };

            WeakReferenceMessenger.Default.Send(message);

            // 更新状态
            StatusText = "扫描完成";
            StatusColor = Colors.Green;

            // 如果不是最后一个字段，自动失去焦点
            if (!IsFinalManualEntry)
            {
                entry.Unfocus();
            }
        }
        catch (Exception ex)
        {
            StatusText = $"处理失败: {ex.Message}";
            StatusColor = Colors.Red;
        }
    }

    private void OnEntryFocused(object sender, FocusEventArgs e)
    {
        // 手动点击时，发送焦点变更消息
        if (!IsActive)
        {
            WeakReferenceMessenger.Default.Send(new ScanFocusChangedMessage(ControlId));
        }
    }

    private void OnEntryUnfocused(object sender, FocusEventArgs e)
    {
        // 失去焦点时的处理
    }

    public void FocusEntry()
    {
        MainThread.BeginInvokeOnMainThread(() =>
        {
            entry.Focus();
        });
    }

    public void ClearEntry()
    {
        MainThread.BeginInvokeOnMainThread(() =>
        {
            ScanText = string.Empty;
            StatusText = "";
            ShowStatus = false;
        });
    }

    #endregion
}