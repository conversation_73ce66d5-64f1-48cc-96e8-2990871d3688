<?xml version="1.0" encoding="utf-8" ?>
<ContentPage xmlns="http://schemas.microsoft.com/dotnet/2021/maui"
             xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
             xmlns:vm="clr-namespace:WMS.APP.ViewModels.inventory"
             x:Class="WMS.APP.Views.inventory.InvTransferPage"
             xmlns:model="clr-namespace:WMS.MODEL;assembly=WMS.MODEL"
             xmlns:toolkit="http://schemas.microsoft.com/dotnet/2022/maui/toolkit"
             x:Name="ParentPage"
             Title="移库操作">
    <ContentPage.BindingContext>
        <vm:InvTransferViewModel />
    </ContentPage.BindingContext>
    <Grid RowDefinitions="Auto,*,Auto">
        <!-- 顶部输入区 -->
        <Grid Grid.Row="0" Padding="16,10,16,0" RowDefinitions="Auto,Auto" ColumnDefinitions="*,Auto" ColumnSpacing="10">
            <Entry Grid.Row="0" Grid.Column="0" Placeholder="原库位：" FontSize="16" Text="{Binding SourceLoc}" />
            <Entry Grid.Row="1" Grid.Column="0" Placeholder="物料号：" FontSize="16" Text="{Binding SkuCode}" />
            <Button Grid.Row="1" Grid.Column="1" Text="🔍查询" FontSize="16" BackgroundColor="Transparent" TextColor="#2196F3"  Command="{Binding QueryInventoryCommand}" />
        </Grid>

        <!-- 横向滚动的库位信息列表 -->
        <CollectionView  Grid.Row="1" ItemsSource="{Binding InventoryList}" >
            <CollectionView.Header>
                <Grid BackgroundColor="LightGray"  ColumnDefinitions="*,*,*,*,*,*,*" >
                    <CheckBox Grid.Column="0"  HorizontalOptions="Center" VerticalOptions="Center" IsChecked="{Binding IsAllChecked}">
                        <CheckBox.Behaviors>
                            <toolkit:EventToCommandBehavior EventName="CheckedChanged" Command="{Binding ToggleAllCommand}"/>
                        </CheckBox.Behaviors>
                    </CheckBox>
                    <Label Grid.Column="1" Text="商品编号" HorizontalOptions="Center" VerticalOptions="Center"/>
                    <Label Grid.Column="2" Text="商品名称"  HorizontalOptions="Center" VerticalOptions="Center"/>
                    <Label Grid.Column="3" Text="批次"  HorizontalOptions="Center" VerticalOptions="Center"/>
                    <Label Grid.Column="4" Text="属性"  HorizontalOptions="Center" VerticalOptions="Center"/>
                    <Label Grid.Column="5" Text="库位"  HorizontalOptions="Center" VerticalOptions="Center"/>
                    <Label Grid.Column="6" Text="数量"  HorizontalOptions="Center" VerticalOptions="Center"/>
                </Grid>
            </CollectionView.Header>
            <CollectionView.ItemTemplate>
                <DataTemplate  x:DataType="model:InventoryModel">
                    <Grid ColumnDefinitions="*,*,*,*,*,*,*" Padding="5">
                        <CheckBox Grid.Column="0"  HorizontalOptions="Center" VerticalOptions="Center" IsChecked="{Binding IsSelected}" >
                            <CheckBox.Behaviors>
                                <toolkit:EventToCommandBehavior EventName="CheckedChanged" Command="{Binding Source={x:Reference ParentPage}, Path=BindingContext.IsCheckCommand}"/>
                            </CheckBox.Behaviors>
                        </CheckBox>
                        <Label Grid.Column="1" Text="{Binding skuCode}" FontSize="14" TextColor="#333" HorizontalOptions="Center" VerticalOptions="Center" />
                        <Label Grid.Column="2" Text="{Binding skuName}" FontSize="14" TextColor="#666" HorizontalOptions="Center" VerticalOptions="Center" />
                        <Label Grid.Column="3" Text="{Binding skuLot}" FontSize="14" TextColor="#666"  HorizontalOptions="Center" VerticalOptions="Center"/>
                        <Label Grid.Column="4" Text="{Binding skuAtr}" FontSize="14" TextColor="#666"  HorizontalOptions="Center" VerticalOptions="Center"/>
                        <Label Grid.Column="5" Text="{Binding locCode}" FontSize="14" TextColor="#666" HorizontalOptions="Center" VerticalOptions="Center" />
                        <Label Grid.Column="6" Text="{Binding skuQty}" FontSize="14" TextColor="#666" HorizontalOptions="Center" VerticalOptions="Center" />
                    </Grid>
                </DataTemplate>
            </CollectionView.ItemTemplate>
        </CollectionView>
        <!-- 底部操作区（可根据实际需求调整） -->
        <Grid Grid.Row="2" Padding="16,8" RowDefinitions="Auto" ColumnDefinitions="Auto,Auto,*,Auto" BackgroundColor="White">
            <CheckBox Grid.Column="0" VerticalOptions="Center" />
            <Label Grid.Column="1" Text="整移    目标库位：" FontSize="16" TextColor="#888" VerticalOptions="Center" />
            <Entry Grid.Column="2" Placeholder="请输入目标库位" FontSize="16" VerticalOptions="Center" />
            <Button Grid.Column="3" Text="⟳ 移库" FontSize="16" TextColor="#2196F3" BackgroundColor="Transparent" Command="{Binding TransferCommand}" />
        </Grid>
    </Grid>
</ContentPage>