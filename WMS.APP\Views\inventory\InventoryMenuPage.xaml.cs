namespace WMS.APP.Views.Inventory;

public partial class InventoryMenuPage : ContentPage
{
    public InventoryMenuPage()
    {
        InitializeComponent();
    }

    private async void OnInvScanClicked(object sender, EventArgs e)
    {
        // 导航到移库操作页面
        await Shell.Current.GoToAsync("//InvScanPage");
    }

    private async void OnInvQueryClicked(object sender, EventArgs e)
    {
        // 导航到库存查询页面
        await Shell.Current.GoToAsync("//InvQueryPage");
    }

    private async void OnInventoryCountClicked(object sender, EventArgs e)
    {
        // 导航到库存盘点页面
        await Shell.Current.GoToAsync("//InventoryCountPage");
    }

    private async void OnInventoryReportClicked(object sender, EventArgs e)
    {
        // 导航到库存报表页面
        await Shell.Current.GoToAsync("//InventoryReportPage");
    }
}
